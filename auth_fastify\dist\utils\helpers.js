"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createFolder = exports.randomId = exports.transformDynamoDBDates = exports.formatDynamoDBTimestamp = void 0;
exports.transformProductData = transformProductData;
const fs_1 = __importDefault(require("fs"));
const crypto_1 = __importDefault(require("crypto"));
// import { toast } from '@/components/ui/use-toast'
// import { EntityError } from '@/lib/http'
// import { type ClassValue, clsx } from 'clsx'
// import { UseFormSetError } from 'react-hook-form'
// import { twMerge } from 'tailwind-merge'
// import jwt from 'jsonwebtoken'
// export function cn(...inputs: ClassValue[]) {
//   return twMerge(clsx(inputs))
// }
/**
 * Converts DynamoDB timestamp to Date object or formatted string
 * @param timestamp DynamoDB timestamp (string or number)
 * @param format Optional format: 'iso' | 'date' | 'datetime' | undefined
 * @returns Date object by default, or formatted string if format is specified
 */
const formatDynamoDBTimestamp = (timestamp, format) => {
    const date = new Date(Number(timestamp));
    switch (format) {
        case 'iso':
            return date.toISOString();
        case 'date':
            return date.toLocaleDateString();
        case 'datetime':
            return date.toLocaleString();
        default:
            return date;
    }
};
exports.formatDynamoDBTimestamp = formatDynamoDBTimestamp;
/**
 * Transforms DynamoDB item timestamps to Date objects
 * @param item DynamoDB item with potential timestamp fields
 * @param timeFields Array of field names that contain timestamps
 * @returns Transformed item with Date objects
 */
const transformDynamoDBDates = (item, timeFields = ['createdAt', 'updatedAt', 'expiresAt']) => {
    const transformed = { ...item };
    timeFields.forEach((field) => {
        if (field in item && item[field]) {
            transformed[field] = (0, exports.formatDynamoDBTimestamp)(item[field]);
        }
    });
    return transformed;
};
exports.transformDynamoDBDates = transformDynamoDBDates;
const randomId = () => crypto_1.default.randomUUID().replace(/-/g, '');
exports.randomId = randomId;
const createFolder = (folderPath) => {
    if (!fs_1.default.existsSync(folderPath)) {
        fs_1.default.mkdirSync(folderPath, { recursive: true });
    }
};
exports.createFolder = createFolder;
function transformProductData(product) {
    return {
        id: String(product.id),
        type: product.type ?? '',
        collection: Number(product.collection),
        prod_id: Number(product.prod_id),
        name: product.name ?? '',
        price: Number(product.price),
        description: product.description ?? '',
        image_url: product.image ?? product.image_url ?? '',
        category: product.category,
        usage: product.usage ?? '',
        gender: product.gender,
        age: product.age,
        plain: product.plain ?? '',
        createdAt: typeof product.createdAt === 'string' ? product.createdAt : product.createdAt?.toISOString?.() ?? '',
        updatedAt: typeof product.updatedAt === 'string' ? product.updatedAt : product.updatedAt?.toISOString?.() ?? '',
        weather: product.weather ?? '',
        color: product.color ?? '',
        color_group: product.color_group ?? '',
        context: product.context ?? '',
        tags: product.tags ?? ''
    };
}
