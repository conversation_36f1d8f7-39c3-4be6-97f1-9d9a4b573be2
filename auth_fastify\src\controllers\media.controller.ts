import { randomId } from '@/utils/helpers'
import { MultipartFile } from '@fastify/multipart'
import path from 'path'
import { S3Helper } from '@/utils/s3Helper'

export const uploadImage = async (data: MultipartFile) => {
  const uniqueId = randomId()
  const ext = path.extname(data.filename)
  const id = uniqueId + ext
  
  // Create S3 helper instance
  const s3Helper = new S3Helper()
  
  // Convert stream to buffer for S3 upload
  const chunks = []
  for await (const chunk of data.file) {
    chunks.push(chunk)
  }
  const buffer = Buffer.concat(chunks)

  if (data.file.truncated) {
    throw new Error('Giới hạn file là 10MB')
  }

  // Upload to S3 with public-read ACL
  const result = await s3Helper.uploadFile(buffer, id, 'products', data.mimetype)

  if (!result.success) {
    throw new Error('Failed to upload image to S3: ' + result.message)
  }

  return result.url
}
