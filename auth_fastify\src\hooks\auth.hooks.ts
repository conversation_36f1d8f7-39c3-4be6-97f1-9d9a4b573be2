import { FastifyReply, FastifyRequest } from 'fastify'
import { getAccountBySessionToken } from '@/controllers/auth.controller'
import { Role } from '@/models/account.model'
import envConfig from '@/config'

export const requireLoginedHook = async (request: FastifyRequest, reply: FastifyReply) => {
  try {
    const sessionToken = envConfig.COOKIE_MODE
      ? request.cookies.sessionToken
      : request.headers.authorization?.split(' ')[1]

    // Check multiple possible header locations for the session token
    // This allows Kong to pass the token through in different header formats
    // const sessionToken = envConfig.COOKIE_MODE
    //   ? request.cookies.sessionToken ||
    //     request.headers['x-session-token'] || // Check for Kong-forwarded header
    //     request.headers['x-auth-token'] || // Alternative header name
    //     request.headers['kong-session-token'] // Another possible Kong header
    //   : request.headers.authorization?.split(' ')[1] ||
    //     request.headers['x-session-token'] || // Check for Kong-forwarded header
    //     request.headers['x-auth-token'] || // Alternative header name
    //     request.headers['kong-session-token'] // Another possible Kong header

    if (!sessionToken) {
      throw new Error('Unauthorized non session')
    }

    const account = await getAccountBySessionToken(sessionToken)
    if (!account) {
      throw new Error('Unauthorized at getAccountBySessionToken')
    }

    // Attach account to request
    request.account = {
      ...account,
      role: account.role as Role
    }
  } catch (error) {
    reply.code(401).send({
      message: `Unauthorized at Catch Error: ${error}`
    })
  }
}
