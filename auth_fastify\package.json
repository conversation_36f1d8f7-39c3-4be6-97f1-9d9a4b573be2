{"name": "server", "version": "1.0.0", "description": "", "main": "index.js", "scripts": {"dev": "npx nodemon", "build": "rimraf ./dist && tsc && tsc-alias", "start": "node dist/index.js", "lint": "eslint .", "lint:fix": "eslint . --fix", "prettier": "prettier --check .", "prettier:fix": "prettier --write .", "migrate": "ts-node prisma/migrations/add_product_fields.ts"}, "keywords": [], "author": "", "license": "ISC", "dependencies": {"@aws-sdk/client-s3": "^3.787.0", "@fastify/auth": "^4.6.0", "@fastify/cookie": "^9.3.1", "@fastify/cors": "^9.0.1", "@fastify/helmet": "^11.1.1", "@fastify/multipart": "^8.1.0", "@fastify/static": "^7.0.1", "@prisma/client": "^5.10.2", "bcrypt": "^5.1.1", "chalk": "^5.3.0", "date-fns": "^3.3.1", "dotenv": "^16.4.5", "dynamoose": "^4.0.4", "fast-jwt": "^4.0.0", "fastify": "^4.26.0", "fastify-plugin": "^4.5.1", "fastify-type-provider-zod": "^1.1.9", "ms": "^2.1.3", "zod": "^3.22.4"}, "devDependencies": {"@types/bcrypt": "^5.0.2", "@types/ms": "^0.7.34", "@types/node": "^20.11.16", "@typescript-eslint/eslint-plugin": "^6.20.0", "@typescript-eslint/parser": "^6.20.0", "eslint": "^8.56.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.3", "nodemon": "^3.0.3", "prettier": "^3.2.5", "prisma": "^5.10.2", "rimraf": "^5.0.5", "ts-node": "^10.9.2", "tsc-alias": "^1.8.8", "tsconfig-paths": "^4.2.0", "tsx": "^4.7.1", "typescript": "^5.3.3"}}