import fs from 'fs'
import crypto from 'crypto'
import { IProduct } from '@/models/product.model';
// import { toast } from '@/components/ui/use-toast'
// import { EntityError } from '@/lib/http'
// import { type ClassValue, clsx } from 'clsx'
// import { UseFormSetError } from 'react-hook-form'
// import { twMerge } from 'tailwind-merge'
// import jwt from 'jsonwebtoken'

// export function cn(...inputs: ClassValue[]) {
//   return twMerge(clsx(inputs))
// }

/**
 * Converts DynamoDB timestamp to Date object or formatted string
 * @param timestamp DynamoDB timestamp (string or number)
 * @param format Optional format: 'iso' | 'date' | 'datetime' | undefined
 * @returns Date object by default, or formatted string if format is specified
 */
export const formatDynamoDBTimestamp = (timestamp: string | number, format?: 'iso' | 'date' | 'datetime') => {
  const date = new Date(Number(timestamp))
  switch (format) {
    case 'iso':
      return date.toISOString()
    case 'date':
      return date.toLocaleDateString()
    case 'datetime':
      return date.toLocaleString()
    default:
      return date
  }
}

/**
 * Transforms DynamoDB item timestamps to Date objects
 * @param item DynamoDB item with potential timestamp fields
 * @param timeFields Array of field names that contain timestamps
 * @returns Transformed item with Date objects
 */
export const transformDynamoDBDates = <T extends Record<string, any>>(
  item: T,
  timeFields: (keyof T)[] = ['createdAt', 'updatedAt', 'expiresAt']
): T => {
  const transformed = { ...item }
  timeFields.forEach((field) => {
    if (field in item && item[field]) {
      transformed[field] = formatDynamoDBTimestamp(item[field]) as T[keyof T]
    }
  })
  return transformed
}

export const randomId = () => crypto.randomUUID().replace(/-/g, '')
export const createFolder = (folderPath: string) => {
  if (!fs.existsSync(folderPath)) {
    fs.mkdirSync(folderPath, { recursive: true })
  }
}

export function transformProductData(product: any) {
  return {
    id: String(product.id),
    type: product.type ?? '',
    collection: Number(product.collection),
    prod_id: Number(product.prod_id),
    name: product.name ?? '',
    price: Number(product.price),
    description: product.description ?? '',
    image_url: product.image ?? product.image_url ?? '',
    category: product.category as 'lower_body' | 'upper_body' | 'dresses',
    usage: product.usage ?? '',
    gender: product.gender as 'male' | 'female' | 'unisex',
    age: product.age as 'young' | 'adult' | 'senior' | 'kid',
    plain: product.plain ?? '',
    createdAt: typeof product.createdAt === 'string' ? product.createdAt : product.createdAt?.toISOString?.() ?? '',
    updatedAt: typeof product.updatedAt === 'string' ? product.updatedAt : product.updatedAt?.toISOString?.() ?? '',
    weather: product.weather ?? '',
    color: product.color ?? '',
    color_group: product.color_group ?? '',
    context: product.context ?? '',
    tags: product.tags ?? ''
  }
}
