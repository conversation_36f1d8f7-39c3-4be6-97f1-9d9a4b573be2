import z from 'zod'

// Basic message response
export const MessageRes = z
  .object({
    message: z.string(),
    statusCode: z.number().optional()
  })
  .strict()

export type MessageResType = z.TypeOf<typeof MessageRes>

// Enhanced error response for validation errors
export const ValidationError = z
  .object({
    code: z.string(),
    message: z.string(),
    path: z.array(z.string().or(z.number())),
    field: z.string().optional()
  })
  .strict()

// Enhanced error response schema
export const ErrorRes = z
  .object({
    message: z.string(),
    statusCode: z.number(),
    errors: z.array(ValidationError).optional(),
    code: z.string().optional(),
    timestamp: z.string().optional().default(() => new Date().toISOString()),
    path: z.string().optional(),
    method: z.string().optional()
  })
  .strict()

export type ErrorResType = z.TypeOf<typeof ErrorRes>

// Pagination metadata schema
export const PaginationMeta = z
  .object({
    page: z.number().int().positive(),
    limit: z.number().int().positive(),
    totalItems: z.number().int().nonnegative(),
    totalPages: z.number().int().nonnegative(),
    hasNextPage: z.boolean(),
    hasPrevPage: z.boolean()
  })
  .strict()

// Generic paginated response schema
export const PaginatedRes = <T extends z.ZodTypeAny>(schema: T) =>
  z
    .object({
      data: z.array(schema),
      meta: PaginationMeta,
      message: z.string()
    })
    .strict()

// Common query parameters for pagination
export const PaginationQuery = z
  .object({
    page: z.coerce.number().int().positive().default(1),
    limit: z.coerce.number().int().positive().default(10)
  })
  .strict()

// Common response wrapper for successful operations
export const SuccessRes = <T extends z.ZodTypeAny>(schema: T) =>
  z
    .object({
      data: schema,
      message: z.string(),
      statusCode: z.number().default(200)
    })
    .strict()

// Common ID parameter schema
export const IdParam = z
  .object({
    id: z.string().uuid({ message: 'Invalid ID format' })
  })
  .strict()

// Common date range query parameters
export const DateRangeQuery = z
  .object({
    startDate: z.coerce.date().optional(),
    endDate: z.coerce.date().optional()
  })
  .strict()
  .refine(
    (data) => {
      if (data.startDate && data.endDate) {
        return data.startDate <= data.endDate
      }
      return true
    },
    {
      message: 'Start date must be before or equal to end date',
      path: ['startDate']
    }
  )

// Common sort query parameters
export const SortQuery = z
  .object({
    sortBy: z.string().optional(),
    sortOrder: z.enum(['asc', 'desc']).optional().default('asc')
  })
  .strict()

// Common search query parameter
export const SearchQuery = z
  .object({
    search: z.string().trim().min(1).max(100).optional()
  })
  .strict()

// Utility type for creating response types
export type PaginatedResType<T extends z.ZodTypeAny> = z.TypeOf<ReturnType<typeof PaginatedRes<T>>>
export type SuccessResType<T extends z.ZodTypeAny> = z.TypeOf<ReturnType<typeof SuccessRes<T>>>

