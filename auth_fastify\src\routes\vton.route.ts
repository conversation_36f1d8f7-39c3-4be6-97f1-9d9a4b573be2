import { FastifyInstance, FastifyPluginOptions } from 'fastify'
import { requireLoginedHook } from '@/hooks/auth.hooks'
import { z } from 'zod'
import { VtonRecordUtils } from '@/models/vton-record.model'

export default async function vtonRoutes(fastify: FastifyInstance, options: FastifyPluginOptions) {
  fastify.get(
    '/history',
    {
      schema: {
        response: {
          200: z.object({
            data: z.array(
              z.object({
                id: z.string(),
                human_url: z.string(),
                garm_url: z.string(),
                res_url: z.string(),
                created_at: z.string()
              })
            ),
            message: z.string()
          })
        }
      },
      preValidation: fastify.auth([requireLoginedHook])
    },
    async (request, reply) => {
      try {
        // Get the user's account ID from the authenticated session
        const accountId = request.account!.id as string

        // Query DynamoDB for the user's VTON records using Dynamoose
        const records = await VtonRecordUtils.getRecentByAccountId(accountId, 5)

        reply.send({
          data: records,
          message: 'VTON history retrieved successfully'
        })
      } catch (error) {
        console.error('Error fetching VTON history:', error)
        reply.status(500).send({
          data: [],
          message: `Failed to retrieve VTON history ${error}`
        })
      }
    }
  )
}
