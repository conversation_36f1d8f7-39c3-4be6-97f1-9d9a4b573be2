"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.authenticate = authenticate;
const session_model_1 = require("../models/session.model");
const account_model_1 = require("../models/account.model");
async function authenticate(request, reply) {
    const token = request.cookies.sessionToken || request.headers.authorization?.replace('Bearer ', '');
    if (!token) {
        reply.code(401).send({ error: 'Authentication required' });
        return;
    }
    const sessions = await session_model_1.Session.scan({
        token: token,
        expiresAt: { gt: new Date() }
    }).exec();
    if (!sessions.count) {
        reply.code(401).send({ error: 'Invalid or expired session' });
        return;
    }
    const session = sessions[0];
    const accounts = await account_model_1.Account.scan({
        id: session.accountId
    }).exec();
    if (!accounts.count) {
        reply.code(401).send({ error: 'Account not found' });
        return;
    }
    request.account = {
        ...accounts[0],
        role: accounts[0].role
    };
    // request.sessionId = session.id;
}
