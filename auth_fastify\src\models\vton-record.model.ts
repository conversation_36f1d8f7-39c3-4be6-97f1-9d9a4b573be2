import * as dynamoose from 'dynamoose'
import { Item } from 'dynamoose/dist/Item'
import envConfig from '@/config'

const vtonRecordSchema = new dynamoose.Schema(
  {
    id: {
      type: String,
      hashKey: true
    },
    trace_id: {
      type: String
    },
    sess_id: {
      type: String
    },
    acc_id: {
      type: String,
      index: [
        {
          type: 'global',
          name: 'accIndex'
        },
        {
          type: 'global',
          name: 'accId_recent_idx',
          rangeKey: 'created_at'
        }
      ]
    },
    human_url: {
      type: String
    },
    garm_url: {
      type: String
    },
    res_url: {
      type: String
    },
    res_msk_url: {
      type: String
    },
    prompt: {
      type: String
    },
    category: {
      type: String,
      enum: ['lower_body', 'upper_body', 'dresses'],
      required: false
    },
    created_at: {
      type: String
    },
    updated_at: {
      type: String
    }
  },
  {
    timestamps: false
  }
)

export interface IVtonRecord extends Item {
  id: string
  trace_id: string
  sess_id: string
  acc_id: string
  human_url: string
  garm_url: string
  res_url: string
  res_msk_url: string
  prompt: string
  category?: string | null
  created_at: string
  updated_at: string
}

export const VtonRecord = dynamoose.model<IVtonRecord>(envConfig.VTON_RECORDS_TABLE, vtonRecordSchema)

// Utility functions
export class VtonRecordUtils {
  static async getRecentByAccountId(accountId: string, limit: number = 5): Promise<IVtonRecord[]> {
    // sort descending by created_at
    const records = await VtonRecord.query('acc_id')
      .eq(accountId)
      .using('accId_recent_idx')
      .sort('descending')
      .limit(limit)
      .exec()
    return records
  }
}
