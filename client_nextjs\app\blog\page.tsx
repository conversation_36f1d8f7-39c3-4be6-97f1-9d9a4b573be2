"use client";
import React, { useState } from "react";

// Demo user (replace with real auth in production)
const demoUser = { id: 1, name: "Demo User" };

interface Post {
  id: number;
  authorId: number;
  authorName: string;
  title: string;
  content: string;
}

export default function BlogPage() {
  const [posts, setPosts] = useState<Post[]>([]);
  const [title, setTitle] = useState("");
  const [content, setContent] = useState("");
  const [editingId, setEditingId] = useState<number | null>(null);
  const [editTitle, setEditTitle] = useState("");
  const [editContent, setEditContent] = useState("");

  const handleCreate = () => {
    if (!title.trim() || !content.trim()) return;
    setPosts([
      ...posts,
      {
        id: Date.now(),
        authorId: demoUser.id,
        authorName: demoUser.name,
        title,
        content,
      },
    ]);
    setTitle("");
    setContent("");
  };

  const handleDelete = (id: number) => {
    setPosts(posts.filter((p) => p.id !== id));
  };

  const handleEdit = (post: Post) => {
    setEditingId(post.id);
    setEditTitle(post.title);
    setEditContent(post.content);
  };

  const handleUpdate = () => {
    setPosts(
      posts.map((p) =>
        p.id === editingId
          ? { ...p, title: editTitle, content: editContent }
          : p
      )
    );
    setEditingId(null);
    setEditTitle("");
    setEditContent("");
  };

  return (
    <main className="min-h-screen bg-gradient-to-br from-gray-100 to-blue-200 p-8">
      <div className="max-w-2xl mx-auto bg-white rounded-xl shadow-lg p-8">
        <h1 className="text-3xl font-bold mb-6 text-blue-700">Blog</h1>
        {/* Create Post */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold mb-2 text-blue-800">Create a Post</h2>
          <input
            className="w-full border rounded px-3 py-2 mb-2 text-black"
            placeholder="Title"
            value={title}
            onChange={(e) => setTitle(e.target.value)}
          />
          <textarea
            className="w-full border rounded px-3 py-2 mb-2 text-black"
            placeholder="Content"
            value={content}
            onChange={(e) => setContent(e.target.value)}
            rows={3}
          />
          <button
            className="bg-blue-600 text-white px-4 py-2 rounded hover:bg-blue-700 transition"
            onClick={handleCreate}
          >
            Post
          </button>
        </div>
        {/* Posts List */} 
        <div>
          {posts.length === 0 && (
            <p className="text-gray-500">No posts yet. Be the first to post!</p>
          )}
          {posts.map((post) => (
            <div
              key={post.id}
              className="mb-6 p-4 border rounded-lg bg-gray-50 shadow-sm" 
            >
              {editingId === post.id ? (
                <div>
                  <input
                    className="w-full border rounded px-3 py-2 mb-2 text-black"
                    value={editTitle}
                    onChange={(e) => setEditTitle(e.target.value)}
                  />
                  <textarea
                    className="w-full border rounded px-3 py-2 mb-2 text-black"
                    value={editContent}
                    onChange={(e) => setEditContent(e.target.value)}
                    rows={3}
                  />
                  <button
                    className="bg-green-600 text-white px-3 py-1 rounded mr-2 hover:bg-green-700"
                    onClick={handleUpdate}
                  >
                    Save
                  </button>
                  <button
                    className="bg-gray-400 text-white px-3 py-1 rounded hover:bg-gray-500"
                    onClick={() => setEditingId(null)}
                  >
                    Cancel
                  </button>
                </div>
              ) : (
                <div>
                  <h3 className="text-lg font-bold text-blue-800">{post.title}</h3>
                  <p className="text-gray-700 mb-2">{post.content}</p>
                  <div className="text-sm text-gray-500 mb-2">
                    By {post.authorName}
                  </div>
                  {post.authorId === demoUser.id && (
                    <div>
                      <button
                        className="bg-yellow-500 text-white px-3 py-1 rounded mr-2 hover:bg-yellow-600"
                        onClick={() => handleEdit(post)}
                      >
                        Edit
                      </button>
                      <button
                        className="bg-red-600 text-white px-3 py-1 rounded hover:bg-red-700"
                        onClick={() => handleDelete(post.id)}
                      >
                        Delete
                      </button>
                    </div>
                  )}
                </div>
              )}
            </div>
          ))}
        </div>
      </div>
    </main>
  );
}
