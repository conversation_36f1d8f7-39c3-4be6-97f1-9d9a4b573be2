import { FastifyRequest } from 'fastify'
import { AuthError } from '@/utils/errors'

type Role = 'USER' | 'MODERATOR' | 'ADMIN'
type Resource = 'products' | 'tryon'
type Action = 'read' | 'create' | 'update' | 'delete' | 'use'

const permissionMatrix: Record<Role, Record<Resource, Action[]>> = {
  USER: {
    products: ['read'],
    tryon: ['use']
  },
  MODERATOR: {
    products: ['read', 'update'],
    tryon: ['use']
  },
  ADMIN: {
    products: ['read', 'create', 'update', 'delete'],
    tryon: ['use']
  }
}

export const requirePermission = (resource: Resource, action: Action) => {
  return async (request: FastifyRequest) => {
    const user = request.account
    if (!user) {
      throw new AuthError('Unauthorized: No user found')
    }

    const allowedActions = permissionMatrix[user.role as Role][resource as Resource] || []
    if (!allowedActions.includes(action)) {
      throw new AuthError(`Unauthorized: Insufficient permissions for ${action} on ${resource}`)
    }
  }
}