"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = productRoutes;
const product_controller_1 = require("../controllers/product.controller");
const abac_middleware_1 = require("../middleware/abac.middleware");
const auth_hooks_1 = require("../hooks/auth.hooks");
const common_schema_1 = require("../schemaValidations/common.schema");
const product_schema_1 = require("../schemaValidations/product.schema");
const helpers_1 = require("../utils/helpers");
const zod_1 = require("zod");
async function productRoutes(fastify, options) {
    // Public route - anyone can read products
    fastify.get('/', {
        schema: {
            response: {
                200: product_schema_1.ProductListRes
            }
        }
    }, async (request, reply) => {
        const products = await (0, product_controller_1.getProductList)();
        const transformedProducts = products.map(helpers_1.transformProductData);
        reply.send({
            data: transformedProducts,
            message: '<PERSON><PERSON><PERSON> danh s<PERSON>ch sản phẩm thành công!'
        });
    });
    fastify.get('/:id', {
        schema: {
            params: zod_1.z.object({
                id: zod_1.z.string()
            }),
            response: {
                200: product_schema_1.ProductRes
            }
        }
    }, async (request, reply) => {
        const product = await (0, product_controller_1.getProductDetail)(request.params.id);
        reply.send({
            data: {
                ...product
            },
            message: 'Lấy thông tin sản phẩm thành công!'
        });
    });
    fastify.post('', {
        schema: {
            body: product_schema_1.CreateProductBody,
            response: {
                200: product_schema_1.ProductRes
            }
        },
        preValidation: fastify.auth([auth_hooks_1.requireLoginedHook, (0, abac_middleware_1.requirePermission)('products', 'create')])
    }, async (request, reply) => {
        // const product = await createProduct(request.body)
        // reply.send({
        //   data: transformProductData(product),
        //   message: 'Tạo sản phẩm thành công!'
        // })
        try {
            fastify.log.info({
                msg: 'Creating new product',
                userId: request.account?.id,
                body: request.body
            });
            const product = await (0, product_controller_1.createProduct)(request.body);
            fastify.log.info({
                msg: 'Product created successfully',
                userId: request.account?.id,
                productId: product.id
            });
            reply.send({
                data: (0, helpers_1.transformProductData)(product),
                message: 'Tạo sản phẩm thành công!'
            });
        }
        catch (error) {
            fastify.log.error({
                msg: 'Failed to create product',
                userId: request.account?.id,
                error: error instanceof Error ? error.message : 'Unknown error',
                stack: error instanceof Error ? error.stack : undefined,
                body: request.body
            });
            throw error; // Let the global error handler handle it
        }
    });
    fastify.put('/:id', {
        schema: {
            params: product_schema_1.ProductParams,
            body: product_schema_1.UpdateProductBody,
            response: {
                200: product_schema_1.ProductRes
            }
        },
        preValidation: fastify.auth([auth_hooks_1.requireLoginedHook, (0, abac_middleware_1.requirePermission)('products', 'update')])
    }, async (request, reply) => {
        const product = await (0, product_controller_1.updateProduct)(request.params.id, request.body);
        reply.send({
            data: (0, helpers_1.transformProductData)(product),
            message: 'Cập nhật sản phẩm thành công!'
        });
    });
    fastify.delete('/:id', {
        schema: {
            params: product_schema_1.ProductParams,
            response: {
                200: common_schema_1.MessageRes
            }
        },
        preValidation: fastify.auth([auth_hooks_1.requireLoginedHook, (0, abac_middleware_1.requirePermission)('products', 'delete')])
    }, async (request, reply) => {
        await (0, product_controller_1.deleteProduct)(request.params.id);
        reply.send({
            message: 'Xóa sản phẩm thành công!'
        });
    });
}
