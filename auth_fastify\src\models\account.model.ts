import * as dynamoose from 'dynamoose'
import { Item } from 'dynamoose/dist/Item'
import envConfig from '@/config'

// Define enum
export enum Role {
  USER = 'USER',
  MODERATOR = 'MODERATOR',
  ADMIN = 'ADMIN'
}

// Define the schema
const accountSchema = new dynamoose.Schema(
  {
    id: {
      type: String,
      hashKey: true
    },
    email: {
      type: String,
      index: {
        type: 'global',
        name: 'emailIndex'
      },
      required: true
    },
    name: {
      type: String,
      required: true
    },
    password: {
      type: String,
      required: true
    },
    role: {
      type: String,
      enum: Object.values(Role),
      default: Role.USER
    },
    createdAt: {
      type: Date,
      default: () => new Date()
    },
    updatedAt: {
      type: Date,
      default: () => new Date()
    }
  },
  {
    // timestamps: true // This will auto-manage createdAt and updatedAt
  }
)

// Define the model interface
export interface IAccount extends Item {
  id: string
  email: string
  name: string
  password: string
  role: Role | string
  createdAt: Date
  updatedAt: Date
}

// Create and export the model
export const Account = dynamoose.model<IAccount>(envConfig.ACCOUNTS_TABLE, accountSchema)
