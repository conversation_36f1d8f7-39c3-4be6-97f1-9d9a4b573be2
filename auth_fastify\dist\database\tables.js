"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.initializeTables = exports.ProductTable = exports.SessionTable = exports.AccountTable = void 0;
const dynamoose = __importStar(require("dynamoose"));
const account_model_1 = require("../models/account.model");
const session_model_1 = require("../models/session.model");
const product_model_1 = require("../models/product.model");
const config_1 = __importDefault(require("../config"));
// Create Table instances with specific configurations
exports.AccountTable = new dynamoose.Table("Accounts", [account_model_1.Account], {
    create: true,
    throughput: {
        read: config_1.default.IS_PRODUCTION ? 5 : 1,
        write: config_1.default.IS_PRODUCTION ? 5 : 1
    },
    waitForActive: {
        enabled: true,
        check: {
            timeout: 180000,
            frequency: 1000
        }
    },
    tags: {
        environment: config_1.default.IS_PRODUCTION ? 'production' : 'development'
    }
});
exports.SessionTable = new dynamoose.Table("Sessions", [session_model_1.Session], {
    create: true,
    throughput: {
        read: config_1.default.IS_PRODUCTION ? 5 : 1,
        write: config_1.default.IS_PRODUCTION ? 5 : 1
    },
    waitForActive: true,
    expires: {
        ttl: 1000 * 60 * 60 * 24 * 7,
        attribute: "expiresAt"
    }
});
exports.ProductTable = new dynamoose.Table("Products", [product_model_1.Product], {
    create: true,
    throughput: {
        read: config_1.default.IS_PRODUCTION ? 5 : 1,
        write: config_1.default.IS_PRODUCTION ? 5 : 1
    },
    waitForActive: true
});
const initializeTables = async () => {
    try {
        await Promise.all([
            exports.AccountTable.initialize(),
            exports.SessionTable.initialize(),
            exports.ProductTable.initialize()
        ]);
        console.log('All DynamoDB tables initialized successfully');
    }
    catch (error) {
        console.error('Error initializing DynamoDB tables:', error);
        throw error;
    }
};
exports.initializeTables = initializeTables;
