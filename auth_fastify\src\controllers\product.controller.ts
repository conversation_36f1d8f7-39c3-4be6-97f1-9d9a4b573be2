import { Product } from '@/models/product.model'
import { CreateProductBodyType, UpdateProductBodyType } from '@/schemaValidations/product.schema'
import { v4 as uuidv4 } from 'uuid'

export const getProductList = async () => {
  return await Product.scan().exec()
}

export const getProductsByCollection = async (collection: number) => {
  return await Product.query('collection').eq(collection).exec()
}

export const getProductsByType = async (type: string) => {
  return await Product.query('type').eq(type).exec()
}

export const getProductsByColor = async (color: string) => {
  return await Product.query('color').eq(color).exec()
}



export const getProductDetail = async (id: string) => {
  const product = await Product.get(id)
  if (!product) throw new Error('Product not found')
  return product
}

export const createProduct = async (data: CreateProductBodyType) => {
  const highestProduct = await Product.scan().attributes(['prod_id']).limit(1).exec()

  const sortedProducts = highestProduct.sort((a, b) => b.prod_id - a.prod_id)
  const nextProdId = (sortedProducts[0]?.prod_id || 0) + 1

  const now = new Date().toISOString()

  // Create the product
  const createdProduct = await Product.create({
    ...data,
    id: uuidv4(),
    prod_id: nextProdId,
    createdAt: now,
    updatedAt: now
  })

  // Return the created product
  return createdProduct
}

export const updateProduct = async (id: string, data: UpdateProductBodyType) => {
  // First get the product to retrieve the collection (sort key)
  const product = await Product.get(id)
  if (!product) throw new Error('Product not found')

  // Update the product while preserving the prod_id and collection
  const { collection, ...restData } = data
  const updatedProduct = await Product.update({
    id,
    collection: product.collection, // Required for update as it's the sort key
    prod_id: product.prod_id, // Preserve the original prod_id
    ...restData,
    updatedAt: new Date().toISOString()
  })

  return updatedProduct
}

export const deleteProduct = async (id: string) => {
  const product = await Product.get(id)
  if (!product) throw new Error('Product not found')

  // Need both hash key and sort key for delete
  await Product.delete({ id, collection: product.collection })
}
