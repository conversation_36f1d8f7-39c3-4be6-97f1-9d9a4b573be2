import envConfig from '@/config'
import {
  login<PERSON><PERSON>roller,
  logoutController,
  slideSessionController,
  registerController
} from '@/controllers/auth.controller'
import { requireLoginedHook } from '@/hooks/auth.hooks'
import {
  LoginBody,
  LoginBodyType,
  LoginRes,
  LoginResType,
  SlideSessionBody,
  SlideSessionBodyType,
  SlideSessionRes,
  SlideSessionResType,
  RegisterBody,
  RegisterBodyType,
  RegisterRes,
  RegisterResType
} from '@/schemaValidations/auth.schema'
import { MessageRes, MessageResType } from '@/schemaValidations/common.schema'
import { FastifyInstance, FastifyPluginOptions } from 'fastify'

export default async function authRoutes(fastify: FastifyInstance, options: FastifyPluginOptions) {
  fastify.post<{
    Reply: RegisterResType
    Body: RegisterBodyType
  }>(
    '/register',
    {
      schema: {
        response: {
          200: RegisterRes
        },
        body: RegisterBody
      }
    },
    async (request, reply) => {
      const { body } = request
      const { session, account } = await registerController(body)
      if (envConfig.COOKIE_MODE) {
        reply
          .setCookie('sessionToken', session.token, {
            path: '/',
            httpOnly: true,
            secure: true,
            expires: session.expiresAt,
            sameSite: 'none',
            domain: envConfig.DOMAIN
          })
          .send({
            message: 'Đăng ký thành công',
            data: {
              token: session.token,
              expiresAt: new Date(session.expiresAt).toISOString(), // Ensure proper date conversion
              account: {
                id: account.id,
                email: account.email,
                name: account.name,
                role: 'USER'
              }
            }
          })
      } else {
        reply.send({
          message: 'Đăng ký thành công',
          data: {
            token: session.token,
            expiresAt: new Date(session.expiresAt).toISOString(), // Ensure proper date conversion
            account: {
              id: account.id,
              email: account.email,
              name: account.name,
              role: 'USER'
            }
          }
        })
      }
    }
  )
  fastify.post<{ Reply: MessageResType }>(
    '/logout',
    {
      schema: {
        response: {
          200: MessageRes
        }
      },
      preValidation: fastify.auth([requireLoginedHook])
    },
    async (request, reply) => {
      const sessionToken = envConfig.COOKIE_MODE
        ? request.cookies.sessionToken
        : request.headers.authorization?.split(' ')[1]
      const message = await logoutController(sessionToken as string)
      if (envConfig.COOKIE_MODE) {
        reply
          .clearCookie('sessionToken', {
            path: '/',
            httpOnly: true,
            sameSite: 'none',
            secure: true
          })
          .send({
            message
          })
      } else {
        reply.send({
          message
        })
      }
    }
  )
  fastify.post<{ Reply: LoginResType; Body: LoginBodyType }>(
    '/login',
    {
      schema: {
        response: {
          200: LoginRes
        },
        body: LoginBody
      }
    },
    async (request, reply) => {
      const { body } = request
      const { session, account } = await loginController(body)
      if (envConfig.COOKIE_MODE) {
        reply
          .setCookie('sessionToken', session.token, {
            path: '/',
            httpOnly: true,
            secure: true,
            expires: session.expiresAt,
            sameSite: 'none',
            domain: envConfig.DOMAIN
          })
          .send({
            message: 'Login Successful',
            data: {
              token: session.token,
              expiresAt: new Date(session.expiresAt).toISOString(), // Ensure proper date conversion
              account: {
                id: account.id,
                email: account.email,
                name: account.name,
                role: account.role // Include role in the response
              }
            }
          })
      } else {
        reply.send({
          message: 'Login Successful',
          data: {
            token: session.token,
            expiresAt: new Date(session.expiresAt).toISOString(), // Ensure proper date conversion
            account: {
              id: account.id,
              email: account.email,
              name: account.name,
              role: account.role as 'USER' | 'MODERATOR' | 'ADMIN'// Include role in the response
            }
          }
        })
      }
    }
  )

  fastify.post<{ Reply: SlideSessionResType; Body: SlideSessionBodyType }>(
    '/slide-session',
    {
      schema: {
        response: {
          200: SlideSessionRes
        },
        body: SlideSessionBody
      },
      preValidation: fastify.auth([requireLoginedHook])
    },
    async (request, reply) => {
      const sessionToken = envConfig.COOKIE_MODE
        ? request.cookies.sessionToken
        : request.headers.authorization?.split(' ')[1]
      const session = await slideSessionController(sessionToken as string)
      if (envConfig.COOKIE_MODE) {
        reply
          .setCookie('sessionToken', session.token, {
            path: '/',
            httpOnly: true,
            secure: true,
            expires: session.expiresAt,
            sameSite: 'none',
            domain: envConfig.DOMAIN
          })
          .send({
            message: 'Refresh session thành công',
            data: {
              token: session.token,
              account: request.account!,
              expiresAt: new Date(session.expiresAt).toISOString() // Ensure proper date conversion
            }
          })
      } else {
        reply.send({
          message: 'Refresh session thành công',
          data: {
            token: session.token,
            expiresAt: new Date(session.expiresAt).toISOString(), // Ensure proper date conversion
            account: request.account!
          }
        })
      }
    }
  )
}
