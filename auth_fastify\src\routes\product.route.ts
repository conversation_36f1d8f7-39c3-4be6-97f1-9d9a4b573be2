import {
  createProduct,
  deleteProduct,
  getProductDetail,
  getProductList,
  updateProduct
} from '@/controllers/product.controller'
import { requirePermission } from '@/middleware/abac.middleware'
import { requireLoginedHook } from '@/hooks/auth.hooks'
import { MessageRes, MessageResType } from '@/schemaValidations/common.schema'
import {
  CreateProductBody,
  CreateProductBodyType,
  ProductListRes,
  ProductListResType,
  ProductParams,
  ProductParamsType,
  ProductRes,
  ProductResType,
  UpdateProductBody,
  UpdateProductBodyType
} from '@/schemaValidations/product.schema'
import { FastifyInstance, FastifyPluginOptions } from 'fastify'
import { transformProductData } from '@/utils/helpers'
import { z } from 'zod'

export default async function productRoutes(fastify: FastifyInstance, options: FastifyPluginOptions) {
  // Public route - anyone can read products
  fastify.get<{ Reply: ProductListResType }>(
    '/',
    {
      schema: {
        response: {
          200: ProductListRes
        }
      }
    },
    async (request, reply) => {
      const products = await getProductList()
      const transformedProducts = products.map(transformProductData)

      reply.send({
        data: transformedProducts,
        message: 'Lấy danh sách sản phẩm thành công!'
      })
    }
  )

  fastify.get<{
    Params: { id: string }
    Reply: ProductResType
  }>(
    '/:id',
    {
      schema: {
        params: z.object({
          id: z.string()
        }),
        response: {
          200: ProductRes
        }
      }
    },
    async (request, reply) => {
      const product = await getProductDetail(request.params.id)
      reply.send({
        data: {
          ...product
        },
        message: 'Lấy thông tin sản phẩm thành công!'
      })
    }
  )

  fastify.post<{
    Body: CreateProductBodyType
    Reply: ProductResType
  }>(
    '',
    {
      schema: {
        body: CreateProductBody,
        response: {
          200: ProductRes
        }
      },
      preValidation: fastify.auth([requireLoginedHook, requirePermission('products', 'create')])
    },
    async (request, reply) => {
      // const product = await createProduct(request.body)
      // reply.send({
      //   data: transformProductData(product),
      //   message: 'Tạo sản phẩm thành công!'
      // })

      try {
        fastify.log.info({
          msg: 'Creating new product',
          userId: request.account?.id,
          body: request.body
        })

        const product = await createProduct(request.body)
        
        fastify.log.info({
          msg: 'Product created successfully',
          userId: request.account?.id,
          productId: product.id
        })

        reply.send({
          data: transformProductData(product),
          message: 'Tạo sản phẩm thành công!'
        })
      } catch (error) {
        fastify.log.error({
          msg: 'Failed to create product',
          userId: request.account?.id,
          error: error instanceof Error ? error.message : 'Unknown error',
          stack: error instanceof Error ? error.stack : undefined,
          body: request.body
        })
        
        throw error // Let the global error handler handle it
      }
    }
  )

  fastify.put<{
    Params: ProductParamsType
    Body: UpdateProductBodyType
    Reply: ProductResType
  }>(
    '/:id',
    {
      schema: {
        params: ProductParams,
        body: UpdateProductBody,
        response: {
          200: ProductRes
        }
      },
      preValidation: fastify.auth([requireLoginedHook, requirePermission('products', 'update')])
    },
    async (request, reply) => {
      const product = await updateProduct(request.params.id, request.body)
      reply.send({
        data: transformProductData(product),
        message: 'Cập nhật sản phẩm thành công!'
      })
    }
  )

  fastify.delete<{
    Params: ProductParamsType
    Reply: MessageResType
  }>(
    '/:id',
    {
      schema: {
        params: ProductParams,
        response: {
          200: MessageRes
        }
      },
      preValidation: fastify.auth([requireLoginedHook, requirePermission('products', 'delete')])
    },
    async (request, reply) => {
      await deleteProduct(request.params.id)
      reply.send({
        message: 'Xóa sản phẩm thành công!'
      })
    }
  )
}
