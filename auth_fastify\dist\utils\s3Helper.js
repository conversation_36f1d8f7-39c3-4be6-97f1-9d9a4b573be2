"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.s3Helper = exports.S3Helper = void 0;
const client_s3_1 = require("@aws-sdk/client-s3");
const config_1 = __importDefault(require("../config"));
class S3Helper {
    s3Client;
    bucket;
    constructor() {
        this.s3Client = new client_s3_1.S3Client({
            region: config_1.default.AWS_REGION,
            credentials: {
                accessKeyId: config_1.default.AWS_ACCESS_KEY_ID,
                secretAccessKey: config_1.default.AWS_SECRET_ACCESS_KEY
            }
        });
        this.bucket = config_1.default.S3_BUCKET;
    }
    async folderExists(folderName) {
        try {
            // Ensure folder name ends with '/'
            const normalizedName = folderName.endsWith('/') ? folderName : `${folderName}/`;
            await this.s3Client.send(new client_s3_1.HeadObjectCommand({
                Bucket: this.bucket,
                Key: normalizedName
            }));
            return true;
        }
        catch (error) {
            if (error.name === 'NotFound') {
                return false;
            }
            throw error;
        }
    }
    async createFolder(folderName) {
        try {
            // Validate folder name
            if (!folderName || /[\\{}^%`[\]'"~<>#|]/.test(folderName)) {
                return {
                    success: false,
                    message: 'Invalid folder name. Folder name contains invalid characters'
                };
            }
            // Ensure folder name ends with '/'
            const normalizedName = folderName.endsWith('/') ? folderName : `${folderName}/`;
            // Check if folder already exists
            const exists = await this.folderExists(normalizedName);
            if (exists) {
                return {
                    success: false,
                    message: `Folder ${folderName} already exists`
                };
            }
            // Create empty object with folder name as key
            await this.s3Client.send(new client_s3_1.PutObjectCommand({
                Bucket: this.bucket,
                Key: normalizedName,
                Body: ''
            }));
            return {
                success: true,
                message: `Folder ${folderName} created successfully`,
                folderName: normalizedName
            };
        }
        catch (error) {
            return {
                success: false,
                message: `Failed to create folder: ${error.message}`
            };
        }
    }
    async uploadFile(file, fileName, folderName, contentType = 'application/octet-stream') {
        try {
            // Validate file name
            if (!fileName || /[\\{}^%`[\]'"~<>#|]/.test(fileName)) {
                return {
                    success: false,
                    message: 'Invalid file name. File name contains invalid characters'
                };
            }
            // Construct the key (path in bucket)
            const key = folderName ? `${folderName.replace(/\/$/, '')}/${fileName}` : fileName;
            // Upload file - removed ACL setting since bucket policy handles public access
            await this.s3Client.send(new client_s3_1.PutObjectCommand({
                Bucket: this.bucket,
                Key: key,
                Body: file,
                ContentType: contentType
                // Removed ACL: 'public-read' since we're using bucket policy instead
            }));
            // Construct the public URL
            const url = `https://${this.bucket}.s3.${config_1.default.AWS_REGION}.amazonaws.com/${key}`;
            return {
                success: true,
                message: 'File uploaded successfully',
                key,
                url
            };
        }
        catch (error) {
            return {
                success: false,
                message: `Failed to upload file: ${error.message}`
            };
        }
    }
    async deleteFile(fileName, folderName) {
        try {
            // Construct the key (path in bucket)
            const key = folderName ? `${folderName.replace(/\/$/, '')}/${fileName}` : fileName;
            // Delete file
            await this.s3Client.send(new client_s3_1.DeleteObjectCommand({
                Bucket: this.bucket,
                Key: key
            }));
            return {
                success: true,
                message: 'File deleted successfully'
            };
        }
        catch (error) {
            return {
                success: false,
                message: `Failed to delete file: ${error.message}`
            };
        }
    }
    async readFile(fileName, folderName) {
        try {
            // Construct the key (path in bucket)
            const key = folderName ? `${folderName.replace(/\/$/, '')}/${fileName}` : fileName;
            // Get file
            const response = await this.s3Client.send(new client_s3_1.GetObjectCommand({
                Bucket: this.bucket,
                Key: key
            }));
            // Convert stream to buffer
            const chunks = [];
            const stream = response.Body;
            for await (const chunk of stream) {
                chunks.push(chunk);
            }
            const buffer = Buffer.concat(chunks);
            return {
                success: true,
                message: 'File read successfully',
                data: buffer,
                contentType: response.ContentType
            };
        }
        catch (error) {
            return {
                success: false,
                message: `Failed to read file: ${error.message}`
            };
        }
    }
}
exports.S3Helper = S3Helper;
// Export a singleton instance
exports.s3Helper = new S3Helper();
