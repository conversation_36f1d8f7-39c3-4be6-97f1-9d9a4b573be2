"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.requirePermission = void 0;
const errors_1 = require("../utils/errors");
const permissionMatrix = {
    USER: {
        products: ['read'],
        tryon: ['use']
    },
    MODERATOR: {
        products: ['read', 'update'],
        tryon: ['use']
    },
    ADMIN: {
        products: ['read', 'create', 'update', 'delete'],
        tryon: ['use']
    }
};
const requirePermission = (resource, action) => {
    return async (request) => {
        const user = request.account;
        if (!user) {
            throw new errors_1.AuthError('Unauthorized: No user found');
        }
        const allowedActions = permissionMatrix[user.role][resource] || [];
        if (!allowedActions.includes(action)) {
            throw new errors_1.AuthError(`Unauthorized: Insufficient permissions for ${action} on ${resource}`);
        }
    };
};
exports.requirePermission = requirePermission;
