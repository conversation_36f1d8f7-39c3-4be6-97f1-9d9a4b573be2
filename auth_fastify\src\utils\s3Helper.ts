import { 
  S3<PERSON>lient, 
  HeadO<PERSON><PERSON>ommand, 
  Put<PERSON><PERSON><PERSON>ommand,
  DeleteObjectCommand,
  GetObjectCommand
} from '@aws-sdk/client-s3'
import envConfig from '@/config'
import { Readable } from 'stream'

export class S3Helper {
  private s3Client: S3Client
  private bucket: string

  constructor() {
    this.s3Client = new S3Client({
      region: envConfig.AWS_REGION,
      credentials: {
        accessKeyId: envConfig.AWS_ACCESS_KEY_ID,
        secretAccessKey: envConfig.AWS_SECRET_ACCESS_KEY
      }
    })
    this.bucket = envConfig.S3_BUCKET
  }

  async folderExists(folderName: string): Promise<boolean> {
    try {
      // Ensure folder name ends with '/'
      const normalizedName = folderName.endsWith('/') ? folderName : `${folderName}/`
      await this.s3Client.send(
        new HeadObjectCommand({
          Bucket: this.bucket,
          Key: normalizedName
        })
      )
      return true
    } catch (error) {
      if ((error as any).name === 'NotFound') {
        return false
      }
      throw error
    }
  }

  async createFolder(folderName: string): Promise<{ 
    success: boolean
    message: string 
    folderName?: string
  }> {
    try {
      // Validate folder name
      if (!folderName || /[\\{}^%`[\]'"~<>#|]/.test(folderName)) {
        return {
          success: false,
          message: 'Invalid folder name. Folder name contains invalid characters'
        }
      }

      // Ensure folder name ends with '/'
      const normalizedName = folderName.endsWith('/') ? folderName : `${folderName}/`

      // Check if folder already exists
      const exists = await this.folderExists(normalizedName)
      if (exists) {
        return {
          success: false,
          message: `Folder ${folderName} already exists`
        }
      }

      // Create empty object with folder name as key
      await this.s3Client.send(
        new PutObjectCommand({
          Bucket: this.bucket,
          Key: normalizedName,
          Body: ''
        })
      )

      return {
        success: true,
        message: `Folder ${folderName} created successfully`,
        folderName: normalizedName
      }
    } catch (error) {
      return {
        success: false,
        message: `Failed to create folder: ${(error as Error).message}`
      }
    }
  }

  async uploadFile(
    file: Buffer | Readable, 
    fileName: string, 
    folderName?: string,
    contentType = 'application/octet-stream'
  ): Promise<{
    success: boolean
    message: string
    key?: string
    url?: string
  }> {
    try {
      // Validate file name
      if (!fileName || /[\\{}^%`[\]'"~<>#|]/.test(fileName)) {
        return {
          success: false,
          message: 'Invalid file name. File name contains invalid characters'
        }
      }

      // Construct the key (path in bucket)
      const key = folderName ? `${folderName.replace(/\/$/, '')}/${fileName}` : fileName

      // Upload file - removed ACL setting since bucket policy handles public access
      await this.s3Client.send(
        new PutObjectCommand({
          Bucket: this.bucket,
          Key: key,
          Body: file,
          ContentType: contentType
          // Removed ACL: 'public-read' since we're using bucket policy instead
        })
      )

      // Construct the public URL
      const url = `https://${this.bucket}.s3.${envConfig.AWS_REGION}.amazonaws.com/${key}`

      return {
        success: true,
        message: 'File uploaded successfully',
        key,
        url
      }
    } catch (error) {
      return {
        success: false,
        message: `Failed to upload file: ${(error as Error).message}`
      }
    }
  }

  async deleteFile(
    fileName: string, 
    folderName?: string
  ): Promise<{
    success: boolean
    message: string
  }> {
    try {
      // Construct the key (path in bucket)
      const key = folderName ? `${folderName.replace(/\/$/, '')}/${fileName}` : fileName

      // Delete file
      await this.s3Client.send(
        new DeleteObjectCommand({
          Bucket: this.bucket,
          Key: key
        })
      )

      return {
        success: true,
        message: 'File deleted successfully'
      }
    } catch (error) {
      return {
        success: false,
        message: `Failed to delete file: ${(error as Error).message}`
      }
    }
  }

  async readFile(
    fileName: string, 
    folderName?: string
  ): Promise<{
    success: boolean
    message: string
    data?: Buffer
    contentType?: string
  }> {
    try {
      // Construct the key (path in bucket)
      const key = folderName ? `${folderName.replace(/\/$/, '')}/${fileName}` : fileName

      // Get file
      const response = await this.s3Client.send(
        new GetObjectCommand({
          Bucket: this.bucket,
          Key: key
        })
      )

      // Convert stream to buffer
      const chunks: Uint8Array[] = []
      const stream = response.Body as Readable
      
      for await (const chunk of stream) {
        chunks.push(chunk as Uint8Array)
      }
      
      const buffer = Buffer.concat(chunks)

      return {
        success: true,
        message: 'File read successfully',
        data: buffer,
        contentType: response.ContentType
      }
    } catch (error) {
      return {
        success: false,
        message: `Failed to read file: ${(error as Error).message}`
      }
    }
  }
}

// Export a singleton instance
export const s3Helper = new S3Helper()

