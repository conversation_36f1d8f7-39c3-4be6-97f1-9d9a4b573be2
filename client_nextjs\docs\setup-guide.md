# MineCraft Blog Setup Guide

## Prerequisites

- Node.js 18+ installed
- Google Cloud Console account for OAuth setup
- Database (PostgreSQL recommended)

## Environment Setup

1. **Copy the environment template:**
   ```bash
   cp .env.local.example .env.local
   ```

2. **Configure Google OAuth:**
   
   a. Go to [Google Cloud Console](https://console.cloud.google.com/)
   
   b. Create a new project or select existing one
   
   c. Enable Google+ API
   
   d. Go to "Credentials" → "Create Credentials" → "OAuth 2.0 Client IDs"
   
   e. Set application type to "Web application"
   
   f. Add authorized redirect URIs:
      - `http://localhost:3000/api/auth/callback/google` (development)
      - `https://yourdomain.com/api/auth/callback/google` (production)
   
   g. Copy Client ID and Client Secret to your `.env.local`:
   ```env
   GOOGLE_CLIENT_ID=your_google_client_id_here
   GOOGLE_CLIENT_SECRET=your_google_client_secret_here
   ```

3. **Configure NextAuth.js:**
   ```env
   NEXTAUTH_URL=http://localhost:3000
   NEXTAUTH_SECRET=your_random_secret_key_here
   ```
   
   Generate a secret key:
   ```bash
   openssl rand -base64 32
   ```

## Installation

1. **Install dependencies:**
   ```bash
   npm install
   ```

2. **Run development server:**
   ```bash
   npm run dev
   ```

3. **Open your browser:**
   Navigate to `http://localhost:3000`

## Features Implemented

✅ **Home Page with Sections:**
- Hero section with gradient background
- Welcome section with feature cards
- About Us section with mission/values
- Features section highlighting platform benefits
- Call-to-action section
- Professional footer

✅ **Authentication System:**
- Google OAuth integration
- Login/logout functionality
- Protected routes
- Session management

✅ **User Profile Page:**
- User information display
- Account details
- Activity tracking (ready for blog posts)
- Quick action cards

✅ **Responsive Design:**
- Mobile-first approach
- Consistent color scheme (black/grey + white)
- shadcn/ui components
- Smooth transitions and hover effects

## Next Steps

1. **Database Setup:**
   - Choose your database (PostgreSQL recommended)
   - Set up database connection
   - Run migrations using the provided schema

2. **Blog Functionality:**
   - Implement blog post creation
   - Add blog listing page
   - Create blog post detail pages

3. **Additional Features:**
   - Comment system
   - Like/follow functionality
   - Search and filtering
   - Image upload for posts

## File Structure

```
client_nextjs/
├── app/
│   ├── api/auth/[...nextauth]/route.ts
│   ├── login/page.tsx
│   ├── profile/page.tsx
│   ├── layout.tsx
│   ├── page.tsx
│   └── globals.css
├── components/
│   ├── navigation/header.tsx
│   ├── providers/session-provider.tsx
│   └── ui/ (shadcn components)
├── lib/
│   ├── auth.ts
│   └── utils.ts
├── docs/
│   ├── database-schema.md
│   └── setup-guide.md
└── .env.local.example
```

## Troubleshooting

**OAuth Issues:**
- Ensure redirect URIs match exactly
- Check that Google+ API is enabled
- Verify environment variables are set correctly

**Build Issues:**
- Clear `.next` folder and rebuild
- Check for TypeScript errors
- Ensure all dependencies are installed

**Styling Issues:**
- Verify Tailwind CSS is configured properly
- Check that shadcn/ui components are installed
- Ensure CSS variables are defined in globals.css
