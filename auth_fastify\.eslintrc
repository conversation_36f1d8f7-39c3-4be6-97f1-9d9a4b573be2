{"root": true, "parser": "@typescript-eslint/parser", "plugins": ["@typescript-eslint", "prettier"], "extends": ["eslint:recommended", "plugin:@typescript-eslint/recommended", "eslint-config-prettier", "prettier"], "rules": {"@typescript-eslint/no-explicit-any": "off", "@typescript-eslint/no-unused-vars": "off", "prettier/prettier": ["warn", {"arrowParens": "always", "semi": false, "trailingComma": "none", "tabWidth": 2, "endOfLine": "auto", "useTabs": false, "singleQuote": true, "printWidth": 120, "jsxSingleQuote": true}]}}