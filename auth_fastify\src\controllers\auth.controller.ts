import { Account, IAccount } from '@/models/account.model'
import { Session, ISession } from '@/models/session.model'
import { LoginBodyType, RegisterBodyType } from '@/schemaValidations/auth.schema'
import { comparePassword, hashPassword } from '@/utils/crypto'
import { EntityError } from '@/utils/errors'
import { signSessionToken } from '@/utils/jwt'
import { addMilliseconds } from 'date-fns'
import { Role } from '@/models/account.model'
import ms from 'ms'
import envConfig from '@/config'
import { v4 as uuidv4 } from 'uuid'

export const registerController = async (body: RegisterBodyType) => {
  try {
    // Check if email already exists
    const existingAccount = await Account.scan({ email: body.email }).exec()
    if (existingAccount.count > 0) {
      throw new EntityError([{ field: 'email', message: 'Email đã tồn tại' }])
    }

    const hashedPassword = await hashPassword(body.password)

    // Check if this is the first account (make it ADMIN)
    const allAccounts = await Account.scan().count().exec()
    const role = allAccounts.count === 0 ? 'ADMIN' : body.role || 'USER'

    // Create new account
    const account = await Account.create({
      id: uuidv4(),
      name: body.name,
      email: body.email,
      password: hashedPassword,
      role: role as Role
    })

    const sessionToken = signSessionToken({
      userId: account.id
    })

    const expiresAt = new Date(Date.now() + ms(envConfig.SESSION_TOKEN_EXPIRES_IN))

    // Create session
    const session = await Session.create({
      id: uuidv4(),
      token: sessionToken,
      accountId: account.id,
      expiresAt: expiresAt
    })

    // Ensure the session object has the correct date format
    const formattedSession = {
      ...session,
      expiresAt: new Date(session.expiresAt),
      createdAt: new Date(session.createdAt)
    }

    return {
      account,
      session: formattedSession
    }
  } catch (error: any) {
    // Re-throw EntityError
    if (error instanceof EntityError) {
      throw error
    }
    // Handle other errors
    throw new Error('Registration failed: ' + error.message)
  }
}

export const loginController = async (body: LoginBodyType) => {
  // Find account by email
  const accounts = await Account.scan({ email: body.email }).exec()
  if (!accounts.count) {
    throw new EntityError([{ field: 'email', message: 'Email không tồn tại' }])
  }

  const account = accounts[0]

  // Verify password
  const isPasswordMatch = await comparePassword(body.password, account.password)
  if (!isPasswordMatch) {
    throw new EntityError([{ field: 'password', message: 'Email hoặc mật khẩu không đúng' }])
  }

  const sessionToken = signSessionToken({
    userId: account.id
  })

  const expiresAt = addMilliseconds(new Date(), ms(envConfig.SESSION_TOKEN_EXPIRES_IN))

  // Create new session
  const session = await Session.create({
    id: uuidv4(),
    token: sessionToken,
    accountId: account.id,
    expiresAt: expiresAt
  })

  // Ensure the session object has the correct date format
  const formattedSession = {
    ...session,
    expiresAt: new Date(session.expiresAt),
    createdAt: new Date(session.createdAt)
  }

  return {
    account,
    session: formattedSession
  }
}

export const logoutController = async (sessionToken: string) => {
  try {
    if (!sessionToken) {
      throw new Error('Session token is required')
    }

    // Find and delete session by token
    const sessions = await Session.scan({ token: sessionToken }).exec()
    if (!sessions || sessions.count === 0) {
      return 'Logout succesfully' // Still return success even if session not found
    }

    await Session.delete(sessions[0].id)
    return 'Logout succesfully, Session deleted'
  } catch (error) {
    console.error('Logout error:', error)
    throw new Error('Lỗi khi đăng xuất: ' + (error as Error).message)
  }
}

export const slideSessionController = async (sessionToken: string) => {
  // Find session
  const sessions = await Session.scan({ token: sessionToken }).exec()
  if (!sessions.count) {
    throw new Error('Session not found')
  }

  const session = sessions[0]
  const expiresAt = addMilliseconds(new Date(), ms(envConfig.SESSION_TOKEN_EXPIRES_IN))

  // Update session expiration
  const updatedSession = await Session.update({
    id: session.id,
    expiresAt: expiresAt
  })

  return updatedSession
}

// Utility function to get account by session token
export const getAccountBySessionToken = async (sessionToken: string): Promise<IAccount | null> => {
  const sessions = await Session.scan({
    token: sessionToken,
    expiresAt: { gt: Date.now() } // Only valid sessions
  }).exec()

  if (!sessions.count) {
    return null
  }

  const accounts = await Account.scan({ id: sessions[0].accountId }).exec()
  return accounts.count ? accounts[0] : null
}
