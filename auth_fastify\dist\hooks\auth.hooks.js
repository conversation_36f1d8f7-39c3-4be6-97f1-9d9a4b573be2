"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.requireLoginedHook = void 0;
const auth_controller_1 = require("../controllers/auth.controller");
const config_1 = __importDefault(require("../config"));
const requireLoginedHook = async (request, reply) => {
    try {
        const sessionToken = config_1.default.COOKIE_MODE
            ? request.cookies.sessionToken
            : request.headers.authorization?.split(' ')[1];
        // Check multiple possible header locations for the session token
        // This allows <PERSON> to pass the token through in different header formats
        // const sessionToken = envConfig.COOKIE_MODE
        //   ? request.cookies.sessionToken ||
        //     request.headers['x-session-token'] || // Check for Kong-forwarded header
        //     request.headers['x-auth-token'] || // Alternative header name
        //     request.headers['kong-session-token'] // Another possible Kong header
        //   : request.headers.authorization?.split(' ')[1] ||
        //     request.headers['x-session-token'] || // Check for Kong-forwarded header
        //     request.headers['x-auth-token'] || // Alternative header name
        //     request.headers['kong-session-token'] // Another possible Kong header
        if (!sessionToken) {
            throw new Error('Unauthorized non session');
        }
        const account = await (0, auth_controller_1.getAccountBySessionToken)(sessionToken);
        if (!account) {
            throw new Error('Unauthorized at getAccountBySessionToken');
        }
        // Attach account to request
        request.account = {
            ...account,
            role: account.role
        };
    }
    catch (error) {
        reply.code(401).send({
            message: `Unauthorized at Catch Error: ${error}`
        });
    }
};
exports.requireLoginedHook = requireLoginedHook;
