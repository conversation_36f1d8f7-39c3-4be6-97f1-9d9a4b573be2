import { FastifyInstance, FastifyPluginOptions } from 'fastify'

export default async function testRoutes(fastify: FastifyInstance, options: FastifyPluginOptions) {
  fastify.get('/health', async (request, reply) => {
    return {
      status: 'ok',
      timestamp: new Date().toISOString(),
      service: 'api-server',
      version: process.env.npm_package_version || '1.0.0'
    }
  })

  fastify.get('/connection-test', async (request, reply) => {
    return {
      status: 'ok',
      clientIp: request.ip,
      headers: request.headers,
      cookies: request.cookies,
      auth: request.headers.authorization ? 'present' : 'missing'
    }
  })
}

