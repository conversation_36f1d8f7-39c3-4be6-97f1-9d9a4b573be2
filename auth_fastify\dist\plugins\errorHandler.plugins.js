"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.errorHandlerPlugin = void 0;
const errors_1 = require("../utils/errors");
const fastify_plugin_1 = __importDefault(require("fastify-plugin"));
const zod_1 = require("zod");
const isZodFastifyError = (error) => {
    if (error instanceof zod_1.ZodError) {
        return true;
    }
    return false;
};
const isEntityError = (error) => {
    if (error instanceof errors_1.EntityError) {
        return true;
    }
    return false;
};
const isAuthError = (error) => {
    if (error instanceof errors_1.AuthError) {
        return true;
    }
    return false;
};
const isForbiddenError = (error) => {
    if (error instanceof errors_1.ForbiddenError) {
        return true;
    }
    return false;
};
const isStatusError = (error) => {
    if (error instanceof errors_1.StatusError) {
        return true;
    }
    return false;
};
exports.errorHandlerPlugin = (0, fastify_plugin_1.default)(async (fastify) => {
    fastify.setErrorHandler(function (error, request, reply) {
        const errorResponse = {
            message: 'An error occurred',
            statusCode: 500,
            timestamp: new Date().toISOString(),
            path: request.url,
            method: request.method
        };
        if (isEntityError(error)) {
            return reply.status(error.status).send({
                ...errorResponse,
                message: 'Lỗi xảy ra khi xác thực dữ liệu...',
                errors: error.fields.map(field => ({
                    code: 'VALIDATION_ERROR',
                    message: field.message,
                    path: [field.field],
                    field: field.field
                })),
                statusCode: error.status
            });
        }
        else if (isForbiddenError(error)) {
            return reply.status(error.status).send({
                message: error.message,
                statusCode: error.status
            });
        }
        else if (isAuthError(error)) {
            return reply
                .setCookie('sessionToken', '', {
                path: '/',
                httpOnly: true,
                sameSite: 'none',
                secure: true
            })
                .status(error.status)
                .send({
                message: error.message,
                statusCode: error.status
            });
        }
        else if (isStatusError(error)) {
            return reply.status(error.status).send({
                message: error.message,
                statusCode: error.status
            });
        }
        else if (isZodFastifyError(error)) {
            const statusCode = 422;
            return reply.status(statusCode).send({
                ...errorResponse,
                message: `Validation error in ${error.validationContext}`,
                errors: error.issues.map(issue => ({
                    code: issue.code,
                    message: issue.message,
                    path: issue.path,
                    field: issue.path.join('.')
                })),
                statusCode
            });
        }
        else {
            const statusCode = error.statusCode || 400;
            return reply.status(statusCode).send({
                message: error.message,
                error,
                statusCode
            });
        }
    });
});
