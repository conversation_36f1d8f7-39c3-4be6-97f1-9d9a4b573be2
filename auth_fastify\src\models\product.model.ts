import * as dynamoose from 'dynamoose'
import { Item } from 'dynamoose/dist/Item'
import envConfig from '@/config'

const productSchema = new dynamoose.Schema(
  {
    id: {
      type: String,
      hashKey: true
    },
    collection: {
      type: Number,
      rangeKey: true
    },
    prod_id: {
      type: Number
    },
    type: {
      type: String,
      index: {
        type: 'global',
        name: 'typeIndex'
      }
    },
    name: String,
    price: Number,
    description: String,
    image_url: String,
    category: {
      type: String,
      enum: ['lower_body', 'upper_body', 'dresses']
    },
    usage: String,
    gender: {
      type: String,
      enum: ['male', 'female', 'unisex']
    },
    age: {
      type: String,
      enum: ['young', 'adult', 'senior', 'kid']
    },
    plain: {
      type: Boolean,
      default: false
    },
    color: {
      type: String,
      index: {
        type: 'global',
        name: 'colorIndex'
      }
    },
    color_group: String,
    context: String,
    tags: String,
    weather: String,
    createdAt: String,
    updatedAt: String
  },
  {
    timestamps: false
  }
)

export interface IProduct extends Item {
  id: string
  collection: number
  prod_id: number
  type: string
  name: string
  price: number
  description: string
  image_url: string
  category: 'lower_body' | 'upper_body' | 'dresses'
  usage: string
  gender: 'male' | 'female' | 'unisex'
  age: 'young' | 'adult' | 'senior' | 'kid'
  plain: boolean
  color: string
  color_group: string
  context: string
  tags: string
  weather: string
  createdAt: string
  updatedAt: string
}

export const Product = dynamoose.model<IProduct>(envConfig.PRODUCTS_TABLE, productSchema)
