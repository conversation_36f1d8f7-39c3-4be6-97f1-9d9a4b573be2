import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { auth, signOut } from "@/lib/auth";
import { redirect } from "next/navigation";
import Link from "next/link";
import Image from "next/image";

export default async function ProfilePage() {
  const session = await auth();
  
  // Redirect if not logged in
  if (!session) {
    redirect('/login');
  }

  const user = session.user;

  return (
    <div className="min-h-screen bg-background">
      {/* Navigation Header */}
      <header className="border-b border-border bg-card">
        <div className="container mx-auto px-4 py-4 flex justify-between items-center">
          <Link href="/" className="flex items-center space-x-2">
            <div className="w-8 h-8 bg-primary rounded-md flex items-center justify-center">
              <span className="text-primary-foreground font-bold text-sm">MC</span>
            </div>
            <h1 className="text-xl font-bold text-foreground">MineCraft Blog</h1>
          </Link>
          <nav className="hidden md:flex items-center space-x-6">
            <Link href="/" className="text-muted-foreground hover:text-primary transition-colors">
              Home
            </Link>
            <Link href="/blogs" className="text-muted-foreground hover:text-primary transition-colors">
              Blogs
            </Link>
            <Link href="/profile" className="text-foreground hover:text-primary transition-colors">
              Profile
            </Link>
          </nav>
          <form
            action={async () => {
              "use server";
              await signOut({ redirectTo: "/" });
            }}
          >
            <Button variant="outline" size="sm" type="submit">
              Sign Out
            </Button>
          </form>
        </div>
      </header>

      <main className="container mx-auto px-4 py-8 max-w-4xl">
        {/* Profile Header */}
        <div className="mb-8">
          <Card className="border-2">
            <CardHeader className="pb-4">
              <div className="flex items-center space-x-4">
                {user?.image && (
                  <Image
                    src={user.image}
                    alt={user.name || "User avatar"}
                    width={80}
                    height={80}
                    className="rounded-full border-2 border-border"
                  />
                )}
                <div className="flex-1">
                  <CardTitle className="text-2xl">{user?.name}</CardTitle>
                  <CardDescription className="text-lg">{user?.email}</CardDescription>
                  <div className="flex items-center space-x-2 mt-2">
                    <Badge variant="secondary">Minecraft Blogger</Badge>
                    <Badge variant="outline">Member since {new Date().getFullYear()}</Badge>
                  </div>
                </div>
              </div>
            </CardHeader>
          </Card>
        </div>

        {/* Profile Stats */}
        <div className="grid md:grid-cols-3 gap-6 mb-8">
          <Card className="text-center">
            <CardHeader>
              <CardTitle className="text-3xl text-primary">0</CardTitle>
              <CardDescription>Blog Posts</CardDescription>
            </CardHeader>
          </Card>
          <Card className="text-center">
            <CardHeader>
              <CardTitle className="text-3xl text-primary">0</CardTitle>
              <CardDescription>Total Views</CardDescription>
            </CardHeader>
          </Card>
          <Card className="text-center">
            <CardHeader>
              <CardTitle className="text-3xl text-primary">0</CardTitle>
              <CardDescription>Followers</CardDescription>
            </CardHeader>
          </Card>
        </div>

        <Separator className="my-8" />

        {/* Quick Actions */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-foreground mb-4">Quick Actions</h2>
          <div className="grid md:grid-cols-2 gap-4">
            <Card className="hover:border-primary/20 transition-colors cursor-pointer">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                    <span className="text-2xl">✍️</span>
                  </div>
                  <div>
                    <CardTitle>Write New Blog Post</CardTitle>
                    <CardDescription>Share your latest Minecraft adventure</CardDescription>
                  </div>
                </div>
              </CardHeader>
            </Card>
            
            <Card className="hover:border-primary/20 transition-colors cursor-pointer">
              <CardHeader>
                <div className="flex items-center space-x-3">
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center">
                    <span className="text-2xl">📚</span>
                  </div>
                  <div>
                    <CardTitle>My Blog Posts</CardTitle>
                    <CardDescription>Manage your published content</CardDescription>
                  </div>
                </div>
              </CardHeader>
            </Card>
          </div>
        </div>

        <Separator className="my-8" />

        {/* Account Information */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-foreground mb-4">Account Information</h2>
          <Card>
            <CardContent className="pt-6">
              <div className="space-y-4">
                <div className="grid md:grid-cols-2 gap-4">
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Full Name</label>
                    <p className="text-foreground">{user?.name || "Not provided"}</p>
                  </div>
                  <div>
                    <label className="text-sm font-medium text-muted-foreground">Email Address</label>
                    <p className="text-foreground">{user?.email || "Not provided"}</p>
                  </div>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">User ID</label>
                  <p className="text-foreground font-mono text-sm">{user?.id || "Not available"}</p>
                </div>
                <div>
                  <label className="text-sm font-medium text-muted-foreground">Account Provider</label>
                  <div className="flex items-center space-x-2">
                    <svg className="w-4 h-4" viewBox="0 0 24 24">
                      <path
                        fill="currentColor"
                        d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"
                      />
                      <path
                        fill="currentColor"
                        d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"
                      />
                      <path
                        fill="currentColor"
                        d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"
                      />
                      <path
                        fill="currentColor"
                        d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"
                      />
                    </svg>
                    <span className="text-foreground">Google</span>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Recent Activity */}
        <div className="mb-8">
          <h2 className="text-2xl font-bold text-foreground mb-4">Recent Activity</h2>
          <Card>
            <CardContent className="pt-6">
              <div className="text-center py-8">
                <div className="w-16 h-16 bg-muted/20 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-3xl text-muted-foreground">📝</span>
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">No Activity Yet</h3>
                <p className="text-muted-foreground mb-4">
                  Start by writing your first blog post to see your activity here.
                </p>
                <Button>
                  Write Your First Post
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </main>
    </div>
  );
}
