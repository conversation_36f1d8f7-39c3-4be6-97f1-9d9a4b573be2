import * as dynamoose from 'dynamoose'
import envConfig from '@/config'
import { initializeTables } from './tables'

let initialized = false;

export const initializeDatabase = async () => {
  if (initialized) {
    return;
  }

  try {
    const ddb = new dynamoose.aws.ddb.DynamoDB({
      credentials: {
        accessKeyId: envConfig.AWS_ACCESS_KEY_ID,
        secretAccessKey: envConfig.AWS_SECRET_ACCESS_KEY
      },
      region: envConfig.AWS_REGION
    });
    dynamoose.aws.ddb.set(ddb)

    await initializeTables();
    initialized = true;
    console.log('Database initialized successfully');
  } catch (error) {
    console.error('Failed to initialize database:', error);
    throw error;
  }
};

