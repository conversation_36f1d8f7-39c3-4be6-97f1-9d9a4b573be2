import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { Head<PERSON> } from "@/components/navigation/header";
import Link from "next/link";

export default function Home() {
  return (
    <div className="min-h-screen bg-background">
      <Header />

      <main>
        {/* Hero Section */}
        <section className="py-20 px-4 text-center bg-gradient-to-br from-primary/5 via-background to-secondary/10">
          <div className="container mx-auto max-w-4xl">
            <Badge variant="secondary" className="mb-4">
              Welcome to the Community
            </Badge>
            <h1 className="text-4xl md:text-6xl font-bold text-foreground mb-6">
              Share Your <span className="text-primary">Minecraft</span> Adventures
            </h1>
            <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
              Join thousands of Minecraft enthusiasts sharing builds, tutorials, mods, and epic adventures.
              Create your own blog and become part of our amazing community.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="text-lg px-8">
                Start Blogging
              </Button>
              <Button variant="outline" size="lg" className="text-lg px-8">
                Explore Blogs
              </Button>
            </div>
          </div>
        </section>

        {/* Welcome Section */}
        <section className="py-16 px-4 bg-background">
          <div className="container mx-auto max-w-6xl">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
                Welcome to MineCraft Blog
              </h2>
              <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
                Your ultimate destination for everything Minecraft. Whether you're a builder,
                redstone engineer, or adventure seeker, this is your space to share and discover.
              </p>
            </div>

            <div className="grid md:grid-cols-3 gap-8">
              <Card className="text-center border-2 hover:border-primary/20 transition-colors">
                <CardHeader>
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">🏗️</span>
                  </div>
                  <CardTitle>Share Your Builds</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Showcase your amazing creations, from simple houses to massive castles.
                    Get feedback and inspire others with your architectural masterpieces.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="text-center border-2 hover:border-primary/20 transition-colors">
                <CardHeader>
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">📚</span>
                  </div>
                  <CardTitle>Write Tutorials</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Help fellow players learn new techniques, redstone contraptions,
                    or survival strategies. Share your knowledge and become a community teacher.
                  </CardDescription>
                </CardContent>
              </Card>

              <Card className="text-center border-2 hover:border-primary/20 transition-colors">
                <CardHeader>
                  <div className="w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4">
                    <span className="text-2xl">🎮</span>
                  </div>
                  <CardTitle>Document Adventures</CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription>
                    Chronicle your epic journeys, multiplayer experiences, and memorable moments.
                    Turn your gameplay into engaging stories for the community.
                  </CardDescription>
                </CardContent>
              </Card>
            </div>
          </div>
        </section>

        {/* About Us Section */}
        <section className="py-16 px-4 bg-muted/20">
          <div className="container mx-auto max-w-4xl text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
              About Our Community
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              MineCraft Blog was created by passionate Minecraft players for passionate Minecraft players.
              We believe that every player has unique experiences and knowledge worth sharing.
            </p>
            <div className="grid md:grid-cols-2 gap-8 text-left">
              <Card className="p-6 bg-card/50 backdrop-blur-sm">
                <h3 className="text-xl font-semibold text-foreground mb-3">Our Mission</h3>
                <p className="text-muted-foreground">
                  To create a welcoming space where Minecraft enthusiasts can share their creativity,
                  learn from each other, and build lasting friendships through their shared love of the game.
                </p>
              </Card>
              <Card className="p-6 bg-card/50 backdrop-blur-sm">
                <h3 className="text-xl font-semibold text-foreground mb-3">Our Values</h3>
                <p className="text-muted-foreground">
                  We promote creativity, respect, and collaboration. Every voice matters in our community,
                  from beginners sharing their first builds to veterans teaching advanced techniques.
                </p>
              </Card>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section className="py-16 px-4 bg-gradient-to-r from-secondary/5 to-accent/5">
          <div className="container mx-auto max-w-6xl">
            <div className="text-center mb-12">
              <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-4">
                Why Choose MineCraft Blog?
              </h2>
              <p className="text-lg text-muted-foreground">
                Everything you need to share your Minecraft journey with the world
              </p>
            </div>

            <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
              <Card className="text-center p-6 bg-card/80 backdrop-blur-sm hover:bg-card transition-colors">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-3xl">✍️</span>
                </div>
                <h3 className="font-semibold text-foreground mb-2">Easy Writing</h3>
                <p className="text-sm text-muted-foreground">
                  Simple, intuitive editor for creating beautiful blog posts
                </p>
              </Card>

              <Card className="text-center p-6 bg-card/80 backdrop-blur-sm hover:bg-card transition-colors">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-3xl">📸</span>
                </div>
                <h3 className="font-semibold text-foreground mb-2">Image Support</h3>
                <p className="text-sm text-muted-foreground">
                  Upload and showcase your screenshots and builds
                </p>
              </Card>

              <Card className="text-center p-6 bg-card/80 backdrop-blur-sm hover:bg-card transition-colors">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-3xl">👥</span>
                </div>
                <h3 className="font-semibold text-foreground mb-2">Community</h3>
                <p className="text-sm text-muted-foreground">
                  Connect with like-minded Minecraft enthusiasts
                </p>
              </Card>

              <Card className="text-center p-6 bg-card/80 backdrop-blur-sm hover:bg-card transition-colors">
                <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                  <span className="text-3xl">🔒</span>
                </div>
                <h3 className="font-semibold text-foreground mb-2">Secure</h3>
                <p className="text-sm text-muted-foreground">
                  Safe and secure platform for all your content
                </p>
              </Card>
            </div>
          </div>
        </section>

        {/* Call to Action Section */}
        <section className="py-20 px-4 bg-gradient-to-t from-primary/10 via-primary/5 to-background">
          <div className="container mx-auto max-w-4xl text-center">
            <h2 className="text-3xl md:text-4xl font-bold text-foreground mb-6">
              Ready to Share Your Story?
            </h2>
            <p className="text-lg text-muted-foreground mb-8">
              Join our growing community of Minecraft bloggers. Create your account today and start sharing
              your adventures, builds, and knowledge with players around the world.
            </p>
            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Button size="lg" className="text-lg px-8">
                Create Account
              </Button>
              <Button variant="outline" size="lg" className="text-lg px-8">
                Learn More
              </Button>
            </div>
            <p className="text-sm text-muted-foreground mt-4">
              Already have an account? <Link href="/login" className="text-primary hover:underline">Sign in here</Link>
            </p>
          </div>
        </section>
      </main>

      {/* Footer */}
      <footer className="border-t border-border bg-card py-12 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="grid md:grid-cols-4 gap-8">
            <div>
              <div className="flex items-center space-x-2 mb-4">
                <div className="w-8 h-8 bg-primary rounded-md flex items-center justify-center">
                  <span className="text-primary-foreground font-bold text-sm">MC</span>
                </div>
                <h3 className="font-bold text-foreground">MineCraft Blog</h3>
              </div>
              <p className="text-sm text-muted-foreground">
                The ultimate platform for Minecraft enthusiasts to share their adventures and connect with the community.
              </p>
            </div>

            <div>
              <h4 className="font-semibold text-foreground mb-3">Community</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li><Link href="/blogs" className="hover:text-primary transition-colors">Browse Blogs</Link></li>
                <li><Link href="/authors" className="hover:text-primary transition-colors">Top Authors</Link></li>
                <li><Link href="/categories" className="hover:text-primary transition-colors">Categories</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold text-foreground mb-3">Support</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li><Link href="/help" className="hover:text-primary transition-colors">Help Center</Link></li>
                <li><Link href="/guidelines" className="hover:text-primary transition-colors">Community Guidelines</Link></li>
                <li><Link href="/contact" className="hover:text-primary transition-colors">Contact Us</Link></li>
              </ul>
            </div>

            <div>
              <h4 className="font-semibold text-foreground mb-3">Legal</h4>
              <ul className="space-y-2 text-sm text-muted-foreground">
                <li><Link href="/privacy" className="hover:text-primary transition-colors">Privacy Policy</Link></li>
                <li><Link href="/terms" className="hover:text-primary transition-colors">Terms of Service</Link></li>
                <li><Link href="/cookies" className="hover:text-primary transition-colors">Cookie Policy</Link></li>
              </ul>
            </div>
          </div>

          <Separator className="my-8" />

          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-sm text-muted-foreground">
              © 2024 MineCraft Blog. All rights reserved.
            </p>
            <p className="text-sm text-muted-foreground">
              Made with ❤️ for the Minecraft community
            </p>
          </div>
        </div>
      </footer>
    </div>
  );
}
