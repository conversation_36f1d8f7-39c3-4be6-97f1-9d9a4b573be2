"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = vtonRoutes;
const auth_hooks_1 = require("../hooks/auth.hooks");
const zod_1 = require("zod");
const vton_record_model_1 = require("../models/vton-record.model");
async function vtonRoutes(fastify, options) {
    fastify.get('/history', {
        schema: {
            response: {
                200: zod_1.z.object({
                    data: zod_1.z.array(zod_1.z.object({
                        id: zod_1.z.string(),
                        human_url: zod_1.z.string(),
                        garm_url: zod_1.z.string(),
                        res_url: zod_1.z.string(),
                        created_at: zod_1.z.string()
                    })),
                    message: zod_1.z.string()
                })
            }
        },
        preValidation: fastify.auth([auth_hooks_1.requireLoginedHook])
    }, async (request, reply) => {
        try {
            // Get the user's account ID from the authenticated session
            const accountId = request.account.id;
            // Query DynamoDB for the user's VTON records using Dynamoose
            const records = await vton_record_model_1.VtonRecordUtils.getRecentByAccountId(accountId, 5);
            reply.send({
                data: records,
                message: 'VTON history retrieved successfully'
            });
        }
        catch (error) {
            console.error('Error fetching VTON history:', error);
            reply.status(500).send({
                data: [],
                message: `Failed to retrieve VTON history ${error}`
            });
        }
    });
}
