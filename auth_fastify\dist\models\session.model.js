"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SessionUtils = exports.Session = void 0;
const dynamoose = __importStar(require("dynamoose"));
const config_1 = __importDefault(require("../config"));
const sessionSchema = new dynamoose.Schema({
    id: {
        type: String,
        hashKey: true
    },
    token: {
        type: String,
        index: [
            {
                type: 'global',
                name: 'tokenIndex'
            },
            // {
            //   type: 'global',
            //   name: 'token_recent_idx',
            //   rangeKey: 'createdAt'
            // }
        ],
        required: true
    },
    accountId: {
        type: String,
        required: true,
        index: {
            type: 'global',
            name: 'accountIdIndex'
        }
    },
    expiresAt: {
        type: Date,
        required: true
        // Add a get transformer to ensure it's always a Date object
        // get: (value: unknown) => (value instanceof Date ? value : new Date(value))
    },
    createdAt: {
        type: Date,
        default: () => new Date()
    }
}, {
    timestamps: false
});
exports.Session = dynamoose.model(config_1.default.SESSIONS_TABLE, sessionSchema);
// Utility functions
class SessionUtils {
    static async findValidByToken(req_token) {
        const sessions = await exports.Session.scan({
            token: req_token,
            expiresAt: { gt: Date.now() }
        }).exec();
        return sessions.count ? sessions[0] : null;
    }
    static async deleteByToken(req_token) {
        const sessions = await exports.Session.scan({ token: req_token }).exec();
        if (sessions.count) {
            await exports.Session.delete(sessions[0].id);
        }
    }
    static async cleanupExpired() {
        const expiredSessions = await exports.Session.scan({
            expiresAt: { lt: Date.now() }
        }).exec();
        for (const session of expiredSessions) {
            await exports.Session.delete(session.id);
        }
    }
}
exports.SessionUtils = SessionUtils;
