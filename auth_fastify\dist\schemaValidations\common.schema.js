"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.SearchQuery = exports.SortQuery = exports.DateRangeQuery = exports.IdParam = exports.SuccessRes = exports.PaginationQuery = exports.PaginatedRes = exports.PaginationMeta = exports.ErrorRes = exports.ValidationError = exports.MessageRes = void 0;
const zod_1 = __importDefault(require("zod"));
// Basic message response
exports.MessageRes = zod_1.default
    .object({
    message: zod_1.default.string(),
    statusCode: zod_1.default.number().optional()
})
    .strict();
// Enhanced error response for validation errors
exports.ValidationError = zod_1.default
    .object({
    code: zod_1.default.string(),
    message: zod_1.default.string(),
    path: zod_1.default.array(zod_1.default.string().or(zod_1.default.number())),
    field: zod_1.default.string().optional()
})
    .strict();
// Enhanced error response schema
exports.ErrorRes = zod_1.default
    .object({
    message: zod_1.default.string(),
    statusCode: zod_1.default.number(),
    errors: zod_1.default.array(exports.ValidationError).optional(),
    code: zod_1.default.string().optional(),
    timestamp: zod_1.default.string().optional().default(() => new Date().toISOString()),
    path: zod_1.default.string().optional(),
    method: zod_1.default.string().optional()
})
    .strict();
// Pagination metadata schema
exports.PaginationMeta = zod_1.default
    .object({
    page: zod_1.default.number().int().positive(),
    limit: zod_1.default.number().int().positive(),
    totalItems: zod_1.default.number().int().nonnegative(),
    totalPages: zod_1.default.number().int().nonnegative(),
    hasNextPage: zod_1.default.boolean(),
    hasPrevPage: zod_1.default.boolean()
})
    .strict();
// Generic paginated response schema
const PaginatedRes = (schema) => zod_1.default
    .object({
    data: zod_1.default.array(schema),
    meta: exports.PaginationMeta,
    message: zod_1.default.string()
})
    .strict();
exports.PaginatedRes = PaginatedRes;
// Common query parameters for pagination
exports.PaginationQuery = zod_1.default
    .object({
    page: zod_1.default.coerce.number().int().positive().default(1),
    limit: zod_1.default.coerce.number().int().positive().default(10)
})
    .strict();
// Common response wrapper for successful operations
const SuccessRes = (schema) => zod_1.default
    .object({
    data: schema,
    message: zod_1.default.string(),
    statusCode: zod_1.default.number().default(200)
})
    .strict();
exports.SuccessRes = SuccessRes;
// Common ID parameter schema
exports.IdParam = zod_1.default
    .object({
    id: zod_1.default.string().uuid({ message: 'Invalid ID format' })
})
    .strict();
// Common date range query parameters
exports.DateRangeQuery = zod_1.default
    .object({
    startDate: zod_1.default.coerce.date().optional(),
    endDate: zod_1.default.coerce.date().optional()
})
    .strict()
    .refine((data) => {
    if (data.startDate && data.endDate) {
        return data.startDate <= data.endDate;
    }
    return true;
}, {
    message: 'Start date must be before or equal to end date',
    path: ['startDate']
});
// Common sort query parameters
exports.SortQuery = zod_1.default
    .object({
    sortBy: zod_1.default.string().optional(),
    sortOrder: zod_1.default.enum(['asc', 'desc']).optional().default('asc')
})
    .strict();
// Common search query parameter
exports.SearchQuery = zod_1.default
    .object({
    search: zod_1.default.string().trim().min(1).max(100).optional()
})
    .strict();
