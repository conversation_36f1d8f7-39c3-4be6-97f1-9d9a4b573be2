self.__RSC_SERVER_MANIFEST="{\n  \"node\": {\n    \"00344232de16b139eb0d4d4321b3e0bd2d562e7edc\": {\n      \"workers\": {\n        \"app/page\": {\n          \"moduleId\": \"[project]/.next-internal/server/app/page/actions.js { ACTIONS_MODULE0 => \\\"[project]/components/navigation/header.tsx [app-rsc] (ecmascript)\\\" } [app-rsc] (server actions loader, ecmascript)\",\n          \"async\": false\n        }\n      },\n      \"layer\": {\n        \"app/page\": \"rsc\"\n      }\n    }\n  },\n  \"edge\": {},\n  \"encryptionKey\": \"KtXdWJr2BE3GUPudv14tfIdpdYnGL3h/1RNXMmSTiTE=\"\n}"