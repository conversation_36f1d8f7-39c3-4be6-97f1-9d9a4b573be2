"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.Product = void 0;
const dynamoose = __importStar(require("dynamoose"));
const config_1 = __importDefault(require("../config"));
const productSchema = new dynamoose.Schema({
    id: {
        type: String,
        hashKey: true
    },
    collection: {
        type: Number,
        rangeKey: true
    },
    prod_id: {
        type: Number
    },
    type: {
        type: String,
        index: {
            type: 'global',
            name: 'typeIndex'
        }
    },
    name: String,
    price: Number,
    description: String,
    image_url: String,
    category: {
        type: String,
        enum: ['lower_body', 'upper_body', 'dresses']
    },
    usage: String,
    gender: {
        type: String,
        enum: ['male', 'female', 'unisex']
    },
    age: {
        type: String,
        enum: ['young', 'adult', 'senior', 'kid']
    },
    plain: {
        type: Boolean,
        default: false
    },
    color: {
        type: String,
        index: {
            type: 'global',
            name: 'colorIndex'
        }
    },
    color_group: String,
    context: String,
    tags: String,
    weather: String,
    createdAt: String,
    updatedAt: String
}, {
    timestamps: false
});
exports.Product = dynamoose.model(config_1.default.PRODUCTS_TABLE, productSchema);
