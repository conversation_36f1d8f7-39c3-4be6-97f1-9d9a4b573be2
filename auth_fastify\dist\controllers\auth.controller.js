"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAccountBySessionToken = exports.slideSessionController = exports.logoutController = exports.loginController = exports.registerController = void 0;
const account_model_1 = require("../models/account.model");
const session_model_1 = require("../models/session.model");
const crypto_1 = require("../utils/crypto");
const errors_1 = require("../utils/errors");
const jwt_1 = require("../utils/jwt");
const date_fns_1 = require("date-fns");
const ms_1 = __importDefault(require("ms"));
const config_1 = __importDefault(require("../config"));
const uuid_1 = require("uuid");
const registerController = async (body) => {
    try {
        // Check if email already exists
        const existingAccount = await account_model_1.Account.scan({ email: body.email }).exec();
        if (existingAccount.count > 0) {
            throw new errors_1.EntityError([{ field: 'email', message: 'Email đã tồn tại' }]);
        }
        const hashedPassword = await (0, crypto_1.hashPassword)(body.password);
        // Check if this is the first account (make it ADMIN)
        const allAccounts = await account_model_1.Account.scan().count().exec();
        const role = allAccounts.count === 0 ? 'ADMIN' : body.role || 'USER';
        // Create new account
        const account = await account_model_1.Account.create({
            id: (0, uuid_1.v4)(),
            name: body.name,
            email: body.email,
            password: hashedPassword,
            role: role
        });
        const sessionToken = (0, jwt_1.signSessionToken)({
            userId: account.id
        });
        const expiresAt = new Date(Date.now() + (0, ms_1.default)(config_1.default.SESSION_TOKEN_EXPIRES_IN));
        // Create session
        const session = await session_model_1.Session.create({
            id: (0, uuid_1.v4)(),
            token: sessionToken,
            accountId: account.id,
            expiresAt: expiresAt
        });
        // Ensure the session object has the correct date format
        const formattedSession = {
            ...session,
            expiresAt: new Date(session.expiresAt),
            createdAt: new Date(session.createdAt)
        };
        return {
            account,
            session: formattedSession
        };
    }
    catch (error) {
        // Re-throw EntityError
        if (error instanceof errors_1.EntityError) {
            throw error;
        }
        // Handle other errors
        throw new Error('Registration failed: ' + error.message);
    }
};
exports.registerController = registerController;
const loginController = async (body) => {
    // Find account by email
    const accounts = await account_model_1.Account.scan({ email: body.email }).exec();
    if (!accounts.count) {
        throw new errors_1.EntityError([{ field: 'email', message: 'Email không tồn tại' }]);
    }
    const account = accounts[0];
    // Verify password
    const isPasswordMatch = await (0, crypto_1.comparePassword)(body.password, account.password);
    if (!isPasswordMatch) {
        throw new errors_1.EntityError([{ field: 'password', message: 'Email hoặc mật khẩu không đúng' }]);
    }
    const sessionToken = (0, jwt_1.signSessionToken)({
        userId: account.id
    });
    const expiresAt = (0, date_fns_1.addMilliseconds)(new Date(), (0, ms_1.default)(config_1.default.SESSION_TOKEN_EXPIRES_IN));
    // Create new session
    const session = await session_model_1.Session.create({
        id: (0, uuid_1.v4)(),
        token: sessionToken,
        accountId: account.id,
        expiresAt: expiresAt
    });
    // Ensure the session object has the correct date format
    const formattedSession = {
        ...session,
        expiresAt: new Date(session.expiresAt),
        createdAt: new Date(session.createdAt)
    };
    return {
        account,
        session: formattedSession
    };
};
exports.loginController = loginController;
const logoutController = async (sessionToken) => {
    try {
        if (!sessionToken) {
            throw new Error('Session token is required');
        }
        // Find and delete session by token
        const sessions = await session_model_1.Session.scan({ token: sessionToken }).exec();
        if (!sessions || sessions.count === 0) {
            return 'Logout succesfully'; // Still return success even if session not found
        }
        await session_model_1.Session.delete(sessions[0].id);
        return 'Logout succesfully, Session deleted';
    }
    catch (error) {
        console.error('Logout error:', error);
        throw new Error('Lỗi khi đăng xuất: ' + error.message);
    }
};
exports.logoutController = logoutController;
const slideSessionController = async (sessionToken) => {
    // Find session
    const sessions = await session_model_1.Session.scan({ token: sessionToken }).exec();
    if (!sessions.count) {
        throw new Error('Session not found');
    }
    const session = sessions[0];
    const expiresAt = (0, date_fns_1.addMilliseconds)(new Date(), (0, ms_1.default)(config_1.default.SESSION_TOKEN_EXPIRES_IN));
    // Update session expiration
    const updatedSession = await session_model_1.Session.update({
        id: session.id,
        expiresAt: expiresAt
    });
    return updatedSession;
};
exports.slideSessionController = slideSessionController;
// Utility function to get account by session token
const getAccountBySessionToken = async (sessionToken) => {
    const sessions = await session_model_1.Session.scan({
        token: sessionToken,
        expiresAt: { gt: Date.now() } // Only valid sessions
    }).exec();
    if (!sessions.count) {
        return null;
    }
    const accounts = await account_model_1.Account.scan({ id: sessions[0].accountId }).exec();
    return accounts.count ? accounts[0] : null;
};
exports.getAccountBySessionToken = getAccountBySessionToken;
