"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.VtonRecordUtils = exports.VtonRecord = void 0;
const dynamoose = __importStar(require("dynamoose"));
const config_1 = __importDefault(require("../config"));
const vtonRecordSchema = new dynamoose.Schema({
    id: {
        type: String,
        hashKey: true
    },
    trace_id: {
        type: String
    },
    sess_id: {
        type: String
    },
    acc_id: {
        type: String,
        index: [
            {
                type: 'global',
                name: 'accIndex'
            },
            {
                type: 'global',
                name: 'accId_recent_idx',
                rangeKey: 'created_at'
            }
        ]
    },
    human_url: {
        type: String
    },
    garm_url: {
        type: String
    },
    res_url: {
        type: String
    },
    res_msk_url: {
        type: String
    },
    prompt: {
        type: String
    },
    category: {
        type: String,
        enum: ['lower_body', 'upper_body', 'dresses'],
        required: false
    },
    created_at: {
        type: String
    },
    updated_at: {
        type: String
    }
}, {
    timestamps: false
});
exports.VtonRecord = dynamoose.model(config_1.default.VTON_RECORDS_TABLE, vtonRecordSchema);
// Utility functions
class VtonRecordUtils {
    static async getRecentByAccountId(accountId, limit = 5) {
        // sort descending by created_at
        const records = await exports.VtonRecord.query('acc_id')
            .eq(accountId)
            .using('accId_recent_idx')
            .sort('descending')
            .limit(limit)
            .exec();
        return records;
    }
}
exports.VtonRecordUtils = VtonRecordUtils;
