import * as dynamoose from 'dynamoose'
import { Account } from '../models/account.model'
import { Session } from '../models/session.model'
import { Product } from '../models/product.model'
import envConfig from '@/config'

// Create Table instances with specific configurations
export const AccountTable = new dynamoose.Table("Accounts", [Account], {
  create: true,
  throughput: {
    read: envConfig.IS_PRODUCTION ? 5 : 1,
    write: envConfig.IS_PRODUCTION ? 5 : 1
  },
  waitForActive: {
    enabled: true,
    check: {
      timeout: 180000,
      frequency: 1000
    }
  },
  tags: {
    environment: envConfig.IS_PRODUCTION ? 'production' : 'development'
  }
});

export const SessionTable = new dynamoose.Table("Sessions", [Session], {
  create: true,
  throughput: {
    read: envConfig.IS_PRODUCTION ? 5 : 1,
    write: envConfig.IS_PRODUCTION ? 5 : 1
  },
  waitForActive: true,
  expires: {
    ttl: 1000 * 60 * 60 * 24 * 7,
    attribute: "expiresAt"
  }
});

export const ProductTable = new dynamoose.Table("Products", [Product], {
  create: true,
  throughput: {
    read: envConfig.IS_PRODUCTION ? 5 : 1,
    write: envConfig.IS_PRODUCTION ? 5 : 1
  },
  waitForActive: true
});

export const initializeTables = async () => {
  try {
    await Promise.all([
      AccountTable.initialize(),
      SessionTable.initialize(),
      ProductTable.initialize()
    ]);
    console.log('All DynamoDB tables initialized successfully');
  } catch (error) {
    console.error('Error initializing DynamoDB tables:', error);
    throw error;
  }
};

