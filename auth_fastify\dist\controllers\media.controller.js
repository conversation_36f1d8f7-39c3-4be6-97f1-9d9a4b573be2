"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.uploadImage = void 0;
const helpers_1 = require("../utils/helpers");
const path_1 = __importDefault(require("path"));
const s3Helper_1 = require("../utils/s3Helper");
const uploadImage = async (data) => {
    const uniqueId = (0, helpers_1.randomId)();
    const ext = path_1.default.extname(data.filename);
    const id = uniqueId + ext;
    // Create S3 helper instance
    const s3Helper = new s3Helper_1.S3Helper();
    // Convert stream to buffer for S3 upload
    const chunks = [];
    for await (const chunk of data.file) {
        chunks.push(chunk);
    }
    const buffer = Buffer.concat(chunks);
    if (data.file.truncated) {
        throw new Error('Giới hạn file là 10MB');
    }
    // Upload to S3 with public-read ACL
    const result = await s3Helper.uploadFile(buffer, id, 'products', data.mimetype);
    if (!result.success) {
        throw new Error('Failed to upload image to S3: ' + result.message);
    }
    return result.url;
};
exports.uploadImage = uploadImage;
