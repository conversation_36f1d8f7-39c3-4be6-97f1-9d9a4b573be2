# Introduction about API

This is the API for the Fashine project, built with Node.js + Fastify + DynamoDB. The API is used to manage user accounts, products, and media. The API is also used to authenticate users and authorize access to resources.

- Authentication: Login, Register, Logout
- Account: Get thông tin cá nhân, <PERSON><PERSON><PERSON> nhật thông tin cá nhân
- Product: <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> sản phẩm, <PERSON><PERSON><PERSON>nh sách sản phẩm
- Media: Upload hình ảnh
- Test API


> In `server/.env` there is a property called `COOKIE_MODE`, set it to `true` if you want to use cookie for authentication on the server

## Tech Stacks Used

Node.js + Fastify + DynamoDB

## Setup 

### Environment Variables

- `PORT`: The port the server will run on
- `SESSION_TOKEN_SECRET`: The secret key used to sign the session token
- `SESSION_TOKEN_EXPIRES_IN`: The expiration time of the session token
- `DOMAIN`: The domain the server will run on
- `PROTOCOL`: The protocol the server will run on
- `UPLOAD_FOLDER`: The folder to store uploaded files
- `COOKIE_MODE`: Whether to use cookie for authentication
- `IS_PRODUCTION`: Whether the server is in production mode
- `PRODUCTION_URL`: The URL the server will run on in production mode
- `AWS_ACCESS_KEY_ID`: The AWS access key ID
- `AWS_SECRET_ACCESS_KEY`: The AWS secret access key
- `AWS_REGION`: The AWS region
- `S3_BUCKET`: The S3 bucket name
- `ACCOUNTS_TABLE`: The DynamoDB table name for accounts
- `SESSIONS_TABLE`: The DynamoDB table name for sessions
- `PRODUCTS_TABLE`: The DynamoDB table name for products

### Running the API

- Clone this repo back to local and run the following commands

```bash
cd server
npm i
npm run dev
```

- In case you want to build the project and run it, use the following commands

```bash
npm run build
npm run start
```

## Error Handling

- When the http body is not valid, the server will return an error with status code `422` and the error message like below


Ví dụ dưới đây body thiếu trường `price`

```json
{
  "message": "A validation error occurred when validating the body...",
  "errors": [
    {
      "code": "invalid_type",
      "expected": "number",
      "received": "undefined",
      "path": ["price"],
      "message": "Required",
      "field": "price"
    }
  ],
  "code": "FST_ERR_VALIDATION",
  "statusCode": 422
}
```


- In some cases, the error is not from the client, but from the server, in that case, the error will be returned in the `message` field, and the `statusCode` will be `500`



```json
{
  "message": "Không tìm thấy dữ liệu!",
  "statusCode": 404
}
```

## Chi tiết các API

The API will run on [http://localhost:4000](http://localhost:4000) by default, you can change it in the `.env` file

In API POST, PUT, usually the body sent to the server is JSON, and must have the header `Content-Type: application/json`.

When doing POST, PUT, DELETE, need authentication, you need to send the `sessionToken` in the header `Authorization: Bearer ${sessionToken}`

API upload images need to send the file in the body must have header `Content-Type: multipart/form-data`


The API authenticates users through a session token, this session token is a JWT, this JWT secret key will be saved in the `.env` file and used to create and verify the token



For APIs that require user authentication like the `Account` API cluster, you have 2 ways for the server to know who you are:

1. Send session token via `sessionToken` header
2. Let cookie send automatically (because when calling login or register api, the server will set cookie for you)

### Test API:

- `GET /test`: Test connection
- `GET /test/health`: Test health
- `GET /test/connection-test`: Test connection

### Authentication

- `POST /auth/register`

```json
{
  "name": "Nguyễn Văn A",
  "email": "<EMAIL>",
  "password": "123123",
  "confirmPassword": "123123"
}
```
When register and login successfully, the server will automatically set a cookie for the domain `localhost` with the name `sessionToken`

- `POST /auth/login`

```json
{
  "email": "<EMAIL>",
  "password": "123123"
}
```

- `POST /auth/slide-session`: Increase the expiration time of the session token. Body is `{}`

Request needs to send `sessionToken` (via cookie or Authorization header depending on your mode)

- `POST /auth/logout`: Log out with body as `{}`, authentication required

When logging out, the server will automatically remove the `sessionToken` cookie

### Account: Need Authentication

- `GET /account/me`: get personal info
- `PUT /account/me`: update personal info, wiht body like below

```json
{
  "name": "Nguyễn Văn A"
}
```

### Products

- `GET /products`: get products list
- `POST /products`: Add product (Need Authentication)

  ```json
  {
    "name": "Gray T-Shirt",
    "price": ********,
    "description": "A gray t-shirt",
    "image": "https://s3.amazonaws.com/ais08staticimgbucket/products/image.jpg",
    "category": "lower_body",
    "collection": 2025,
    "type": "áo thun",
    "usage": "casual",
    "gender": "male",
    "age": "young",
    "plain": false,
    "color": "black",
    "color_group": "neutral",
    "context": "phong cách thoải mái, năng động",
    "tags": "áo thun, black, young, male",
    "weather": "nóng"
  }
  ```

- `PUT /products/:id`: update product (Need Authentication)
- `DELETE /products/:id`: Delete product (Need Authentication)
- `GET /products/:id`: Get product detail

