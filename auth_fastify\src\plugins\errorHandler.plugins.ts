import { AuthError, EntityError, ForbiddenError, StatusError } from '@/utils/errors'
import { PrismaClientKnownRequestError } from '@prisma/client/runtime/library'
import { FastifyError, FastifyReply, FastifyRequest } from 'fastify'
import fastifyPlugin from 'fastify-plugin'
import { ZodError } from 'zod'
import { ErrorResType } from '../schemaValidations/common.schema'

type ZodFastifyError = FastifyError & ZodError

const isZodFastifyError = (error: any): error is ZodFastifyError => {
  if (error instanceof ZodError) {
    return true
  }
  return false
}

const isEntityError = (error: any): error is EntityError => {
  if (error instanceof EntityError) {
    return true
  }
  return false
}

const isAuthError = (error: any): error is AuthError => {
  if (error instanceof AuthError) {
    return true
  }
  return false
}

const isForbiddenError = (error: any): error is ForbiddenError => {
  if (error instanceof ForbiddenError) {
    return true
  }
  return false
}

const isStatusError = (error: any): error is StatusError => {
  if (error instanceof StatusError) {
    return true
  }
  return false
}

export const errorHandlerPlugin = fastifyPlugin(async (fastify) => {
  fastify.setErrorHandler(function (
    error: EntityError | AuthError | ForbiddenError | FastifyError | ZodFastifyError | PrismaClientKnownRequestError,
    request: FastifyRequest,
    reply: FastifyReply
  ) {
    const errorResponse: ErrorResType = {
      message: 'An error occurred',
      statusCode: 500,
      timestamp: new Date().toISOString(),
      path: request.url,
      method: request.method
    }

    if (isEntityError(error)) {
      return reply.status(error.status).send({
        ...errorResponse,
        message: 'Lỗi xảy ra khi xác thực dữ liệu...',
        errors: error.fields.map(field => ({
          code: 'VALIDATION_ERROR',
          message: field.message,
          path: [field.field],
          field: field.field
        })),
        statusCode: error.status
      })
    } else if (isForbiddenError(error)) {
      return reply.status(error.status).send({
        message: error.message,
        statusCode: error.status
      })
    } else if (isAuthError(error)) {
      return reply
        .setCookie('sessionToken', '', {
          path: '/',
          httpOnly: true,
          sameSite: 'none',
          secure: true
        })
        .status(error.status)
        .send({
          message: error.message,
          statusCode: error.status
        })
    } else if (isStatusError(error)) {
      return reply.status(error.status).send({
        message: error.message,
        statusCode: error.status
      })
    } else if (isZodFastifyError(error)) {
      const statusCode = 422
      return reply.status(statusCode).send({
        ...errorResponse,
        message: `Validation error in ${error.validationContext}`,
        errors: error.issues.map(issue => ({
          code: issue.code,
          message: issue.message,
          path: issue.path,
          field: issue.path.join('.')
        })),
        statusCode
      })
    } else {
      const statusCode = (error as any).statusCode || 400
      return reply.status(statusCode).send({
        message: error.message,
        error,
        statusCode
      })
    }
  })
})


