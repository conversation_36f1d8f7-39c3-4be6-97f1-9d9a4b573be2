import { Account, IAccount } from '@/models/account.model'
import { UpdateMeBodyType } from '@/schemaValidations/account.schema'
import { EntityError } from '@/utils/errors'

export const updateMeController = async (accountId: string, body: UpdateMeBodyType) => {
  try {
    // Check if account exists
    const existingAccount = await Account.get(accountId)
    if (!existingAccount) {
      throw new Error('Account not found')
    }

    // Update account
    const updatedAccount = await Account.update({
      id: accountId,
      name: body.name,
      updatedAt: new Date() // Ensure updatedAt is set
    })

    // Return only the necessary fields
    return {
      id: updatedAccount.id,
      name: updatedAccount.name,
      email: updatedAccount.email,
      role: updatedAccount.role
    }
  } catch (error: any) {
    if (error instanceof EntityError) {
      throw error
    }
    throw new Error(`Failed to update account: ${error.message}`)
  }
}

// Optional: Add more utility functions for account management
export const getAccountById = async (id: string): Promise<IAccount | null> => {
  try {
    const account = await Account.get(id)
    return account || null
  } catch (error) {
    console.error('Error fetching account:', error)
    return null
  }
}

export const getAccountByEmail = async (email: string): Promise<IAccount | null> => {
  try {
    const accounts = await Account.scan({ email }).exec()
    return accounts.count > 0 ? accounts[0] : null
  } catch (error) {
    console.error('Error fetching account by email:', error)
    return null
  }
}




// import prisma from '@/database'
// import { UpdateMeBodyType } from '@/schemaValidations/account.schema'

// export const updateMeController = async (accountId: string, body: UpdateMeBodyType) => {
//   const account = prisma.account.update({
//     where: {
//       id: accountId
//     },
//     data: {
//       name: body.name
//     }
//   })
//   return account
// }
