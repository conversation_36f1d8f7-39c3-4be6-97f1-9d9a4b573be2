"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProductParams = exports.UpdateProductBody = exports.ProductListRes = exports.ProductRes = exports.ProductSchema = exports.CreateProductBody = void 0;
const zod_1 = __importDefault(require("zod"));
const GenderEnum = zod_1.default.enum(['male', 'female', 'unisex']);
const AgeEnum = zod_1.default.enum(['young', 'adult', 'senior', 'kid']);
exports.CreateProductBody = zod_1.default.object({
    name: zod_1.default.string().min(1).max(256),
    prod_id: zod_1.default.number().optional(), // Make it optional since we'll generate it
    price: zod_1.default.number().positive(),
    description: zod_1.default.string().max(10000),
    image_url: zod_1.default.string().url(),
    category: zod_1.default.enum(['lower_body', 'upper_body', 'dresses']),
    // Updated fields to match the model
    collection: zod_1.default.coerce.number(),
    type: zod_1.default.string(),
    usage: zod_1.default.string(),
    gender: GenderEnum,
    age: AgeEnum,
    plain: zod_1.default.boolean(),
    color: zod_1.default.string(),
    color_group: zod_1.default.string(),
    context: zod_1.default.string(),
    tags: zod_1.default.string(),
    weather: zod_1.default.string()
});
exports.ProductSchema = zod_1.default.object({
    id: zod_1.default.string(),
    prod_id: zod_1.default.number(),
    name: zod_1.default.string(),
    price: zod_1.default.number(),
    description: zod_1.default.string(),
    image_url: zod_1.default.string(),
    category: zod_1.default.enum(['lower_body', 'upper_body', 'dresses']),
    collection: zod_1.default.number(),
    type: zod_1.default.string(),
    usage: zod_1.default.string(),
    gender: GenderEnum,
    age: AgeEnum,
    plain: zod_1.default.boolean(),
    color: zod_1.default.string(),
    color_group: zod_1.default.string(),
    context: zod_1.default.string(),
    tags: zod_1.default.string(),
    weather: zod_1.default.string(),
    createdAt: zod_1.default.string(),
    updatedAt: zod_1.default.string()
});
exports.ProductRes = zod_1.default.object({
    data: exports.ProductSchema,
    message: zod_1.default.string()
});
exports.ProductListRes = zod_1.default.object({
    data: zod_1.default.array(exports.ProductSchema),
    message: zod_1.default.string()
});
exports.UpdateProductBody = exports.CreateProductBody;
exports.ProductParams = zod_1.default.object({
    id: zod_1.default.string()
});
