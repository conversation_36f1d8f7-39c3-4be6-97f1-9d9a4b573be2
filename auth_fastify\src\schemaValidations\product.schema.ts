import z from 'zod'

const GenderEnum = z.enum(['male', 'female', 'unisex'])
const AgeEnum = z.enum(['young', 'adult', 'senior', 'kid'])

export const CreateProductBody = z.object({
  name: z.string().min(1).max(256),
  prod_id: z.number().optional(), // Make it optional since we'll generate it
  price: z.number().positive(),
  description: z.string().max(10000),
  image_url: z.string().url(),
  category: z.enum(['lower_body', 'upper_body', 'dresses']),
  
  // Updated fields to match the model
  collection: z.coerce.number(),
  type: z.string(),
  usage: z.string(),
  gender: GenderEnum,
  age: AgeEnum,
  plain: z.boolean(),
  color: z.string(),
  color_group: z.string(),
  context: z.string(),
  tags: z.string(),
  weather: z.string()
})

export type CreateProductBodyType = z.TypeOf<typeof CreateProductBody>

export const ProductSchema = z.object({
  id: z.string(),
  prod_id: z.number(),
  name: z.string(),
  price: z.number(),
  description: z.string(),
  image_url: z.string(),
  category: z.enum(['lower_body', 'upper_body', 'dresses']),
  collection: z.number(),
  type: z.string(),
  usage: z.string(),
  gender: GenderEnum,
  age: AgeEnum,
  plain: z.boolean(),
  color: z.string(),
  color_group: z.string(),
  context: z.string(),
  tags: z.string(),
  weather: z.string(),
  createdAt: z.string(),
  updatedAt: z.string()
})

export const ProductRes = z.object({
  data: ProductSchema,
  message: z.string()
})

export type ProductResType = z.TypeOf<typeof ProductRes>

export const ProductListRes = z.object({
  data: z.array(ProductSchema),
  message: z.string()
})

export type ProductListResType = z.TypeOf<typeof ProductListRes>

export const UpdateProductBody = CreateProductBody
export type UpdateProductBodyType = CreateProductBodyType
export const ProductParams = z.object({
  id: z.string()
})
export type ProductParamsType = z.TypeOf<typeof ProductParams>


