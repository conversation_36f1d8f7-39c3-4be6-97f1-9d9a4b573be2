import { But<PERSON> } from "@/components/ui/button";
import { auth, signOut } from "@/lib/auth";
import Link from "next/link";
import Image from "next/image";

export async function Header() {
  const session = await auth();

  return (
    <header className="border-b border-border bg-card">
      <div className="container mx-auto px-4 py-4 flex justify-between items-center">
        <Link href="/" className="flex items-center space-x-2">
          <div className="w-8 h-8 bg-primary rounded-md flex items-center justify-center">
            <span className="text-primary-foreground font-bold text-sm">MC</span>
          </div>
          <h1 className="text-xl font-bold text-foreground">MineCraft Blog</h1>
        </Link>
        
        <nav className="hidden md:flex items-center space-x-6">
          <Link href="/" className="text-foreground hover:text-primary transition-colors">
            Home
          </Link>
          <Link href="/blogs" className="text-muted-foreground hover:text-primary transition-colors">
            Blogs
          </Link>
          <Link href="/about" className="text-muted-foreground hover:text-primary transition-colors">
            About
          </Link>
          {session && (
            <Link href="/profile" className="text-muted-foreground hover:text-primary transition-colors">
              Profile
            </Link>
          )}
        </nav>
        
        <div className="flex items-center space-x-2">
          {session ? (
            <div className="flex items-center space-x-3">
              {session.user?.image && (
                <Link href="/profile">
                  <Image
                    src={session.user.image}
                    alt={session.user.name || "User avatar"}
                    width={32}
                    height={32}
                    className="rounded-full border border-border hover:border-primary transition-colors"
                  />
                </Link>
              )}
              <span className="text-sm text-muted-foreground hidden sm:block">
                {session.user?.name}
              </span>
              <form
                action={async () => {
                  "use server";
                  await signOut({ redirectTo: "/" });
                }}
              >
                <Button variant="outline" size="sm" type="submit">
                  Sign Out
                </Button>
              </form>
            </div>
          ) : (
            <>
              <Button variant="ghost" size="sm" asChild>
                <Link href="/login">Login</Link>
              </Button>
              <Button size="sm" asChild>
                <Link href="/login">Sign Up</Link>
              </Button>
            </>
          )}
        </div>
      </div>
    </header>
  );
}
