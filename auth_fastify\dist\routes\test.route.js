"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.default = testRoutes;
async function testRoutes(fastify, options) {
    fastify.get('/health', async (request, reply) => {
        return {
            status: 'ok',
            timestamp: new Date().toISOString(),
            service: 'api-server',
            version: process.env.npm_package_version || '1.0.0'
        };
    });
    fastify.get('/connection-test', async (request, reply) => {
        return {
            status: 'ok',
            clientIp: request.ip,
            headers: request.headers,
            cookies: request.cookies,
            auth: request.headers.authorization ? 'present' : 'missing'
        };
    });
}
