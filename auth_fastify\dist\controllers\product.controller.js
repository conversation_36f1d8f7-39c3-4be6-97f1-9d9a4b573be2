"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.deleteProduct = exports.updateProduct = exports.createProduct = exports.getProductDetail = exports.getProductsByColor = exports.getProductsByType = exports.getProductsByCollection = exports.getProductList = void 0;
const product_model_1 = require("../models/product.model");
const uuid_1 = require("uuid");
const getProductList = async () => {
    return await product_model_1.Product.scan().exec();
};
exports.getProductList = getProductList;
const getProductsByCollection = async (collection) => {
    return await product_model_1.Product.query('collection').eq(collection).exec();
};
exports.getProductsByCollection = getProductsByCollection;
const getProductsByType = async (type) => {
    return await product_model_1.Product.query('type').eq(type).exec();
};
exports.getProductsByType = getProductsByType;
const getProductsByColor = async (color) => {
    return await product_model_1.Product.query('color').eq(color).exec();
};
exports.getProductsByColor = getProductsByColor;
const getProductDetail = async (id) => {
    const product = await product_model_1.Product.get(id);
    if (!product)
        throw new Error('Product not found');
    return product;
};
exports.getProductDetail = getProductDetail;
const createProduct = async (data) => {
    const highestProduct = await product_model_1.Product.scan().attributes(['prod_id']).limit(1).exec();
    const sortedProducts = highestProduct.sort((a, b) => b.prod_id - a.prod_id);
    const nextProdId = (sortedProducts[0]?.prod_id || 0) + 1;
    const now = new Date().toISOString();
    // Create the product
    const createdProduct = await product_model_1.Product.create({
        ...data,
        id: (0, uuid_1.v4)(),
        prod_id: nextProdId,
        createdAt: now,
        updatedAt: now
    });
    // Return the created product
    return createdProduct;
};
exports.createProduct = createProduct;
const updateProduct = async (id, data) => {
    // First get the product to retrieve the collection (sort key)
    const product = await product_model_1.Product.get(id);
    if (!product)
        throw new Error('Product not found');
    // Update the product while preserving the prod_id and collection
    const { collection, ...restData } = data;
    const updatedProduct = await product_model_1.Product.update({
        id,
        collection: product.collection, // Required for update as it's the sort key
        prod_id: product.prod_id, // Preserve the original prod_id
        ...restData,
        updatedAt: new Date().toISOString()
    });
    return updatedProduct;
};
exports.updateProduct = updateProduct;
const deleteProduct = async (id) => {
    const product = await product_model_1.Product.get(id);
    if (!product)
        throw new Error('Product not found');
    // Need both hash key and sort key for delete
    await product_model_1.Product.delete({ id, collection: product.collection });
};
exports.deleteProduct = deleteProduct;
