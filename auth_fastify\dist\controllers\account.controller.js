"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAccountByEmail = exports.getAccountById = exports.updateMeController = void 0;
const account_model_1 = require("../models/account.model");
const errors_1 = require("../utils/errors");
const updateMeController = async (accountId, body) => {
    try {
        // Check if account exists
        const existingAccount = await account_model_1.Account.get(accountId);
        if (!existingAccount) {
            throw new Error('Account not found');
        }
        // Update account
        const updatedAccount = await account_model_1.Account.update({
            id: accountId,
            name: body.name,
            updatedAt: new Date() // Ensure updatedAt is set
        });
        // Return only the necessary fields
        return {
            id: updatedAccount.id,
            name: updatedAccount.name,
            email: updatedAccount.email,
            role: updatedAccount.role
        };
    }
    catch (error) {
        if (error instanceof errors_1.EntityError) {
            throw error;
        }
        throw new Error(`Failed to update account: ${error.message}`);
    }
};
exports.updateMeController = updateMeController;
// Optional: Add more utility functions for account management
const getAccountById = async (id) => {
    try {
        const account = await account_model_1.Account.get(id);
        return account || null;
    }
    catch (error) {
        console.error('Error fetching account:', error);
        return null;
    }
};
exports.getAccountById = getAccountById;
const getAccountByEmail = async (email) => {
    try {
        const accounts = await account_model_1.Account.scan({ email }).exec();
        return accounts.count > 0 ? accounts[0] : null;
    }
    catch (error) {
        console.error('Error fetching account by email:', error);
        return null;
    }
};
exports.getAccountByEmail = getAccountByEmail;
// import prisma from '../database'
// import { UpdateMeBodyType } from '../schemaValidations/account.schema'
// export const updateMeController = async (accountId: string, body: UpdateMeBodyType) => {
//   const account = prisma.account.update({
//     where: {
//       id: accountId
//     },
//     data: {
//       name: body.name
//     }
//   })
//   return account
// }
