{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/NextJS/Minecraft_Web/client_nextjs/lib/utils.ts"], "sourcesContent": ["import { clsx, type ClassValue } from \"clsx\"\nimport { twMerge } from \"tailwind-merge\"\n\nexport function cn(...inputs: ClassValue[]) {\n  return twMerge(clsx(inputs))\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,SAAS,GAAG,GAAG,MAAoB;IACxC,OAAO,CAAA,GAAA,2JAAA,CAAA,UAAO,AAAD,EAAE,CAAA,GAAA,qIAAA,CAAA,OAAI,AAAD,EAAE;AACtB", "debugId": null}}, {"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/NextJS/Minecraft_Web/client_nextjs/components/ui/button.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst buttonVariants = cva(\n  \"inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90\",\n        destructive:\n          \"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50\",\n        secondary:\n          \"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80\",\n        ghost:\n          \"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50\",\n        link: \"text-primary underline-offset-4 hover:underline\",\n      },\n      size: {\n        default: \"h-9 px-4 py-2 has-[>svg]:px-3\",\n        sm: \"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5\",\n        lg: \"h-10 rounded-md px-6 has-[>svg]:px-4\",\n        icon: \"size-9\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n      size: \"default\",\n    },\n  }\n)\n\nfunction Button({\n  className,\n  variant,\n  size,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"button\"> &\n  VariantProps<typeof buttonVariants> & {\n    asChild?: boolean\n  }) {\n  const Comp = asChild ? Slot : \"button\"\n\n  return (\n    <Comp\n      data-slot=\"button\"\n      className={cn(buttonVariants({ variant, size, className }))}\n      {...props}\n    />\n  )\n}\n\nexport { Button, buttonVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,iBAAiB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACvB,+bACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,aACE;YACF,SACE;YACF,WACE;YACF,OACE;YACF,MAAM;QACR;QACA,MAAM;YACJ,SAAS;YACT,IAAI;YACJ,IAAI;YACJ,MAAM;QACR;IACF;IACA,iBAAiB;QACf,SAAS;QACT,MAAM;IACR;AACF;AAGF,SAAS,OAAO,EACd,SAAS,EACT,OAAO,EACP,IAAI,EACJ,UAAU,KAAK,EACf,GAAG,OAIF;IACD,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,eAAe;YAAE;YAAS;YAAM;QAAU;QACvD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 88, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/NextJS/Minecraft_Web/client_nextjs/lib/auth.ts"], "sourcesContent": ["import NextAuth from \"next-auth\"\nimport Google from \"next-auth/providers/google\"\n\nexport const { handlers, signIn, signOut, auth } = NextAuth({\n  providers: [\n    Google({\n      clientId: process.env.GOOGLE_CLIENT_ID!,\n      clientSecret: process.env.GOOGLE_CLIENT_SECRET!,\n    }),\n  ],\n  callbacks: {\n    async session({ session, token }) {\n      if (session.user && token.sub) {\n        session.user.id = token.sub\n      }\n      return session\n    },\n    async jwt({ token, user }) {\n      if (user) {\n        token.sub = user.id\n      }\n      return token\n    },\n  },\n  pages: {\n    signIn: '/login',\n  },\n})\n"], "names": [], "mappings": ";;;;;;AAAA;AAAA;AACA;AAAA;;;AAEO,MAAM,EAAE,QAAQ,EAAE,MAAM,EAAE,OAAO,EAAE,IAAI,EAAE,GAAG,CAAA,GAAA,qJAAA,CAAA,UAAQ,AAAD,EAAE;IAC1D,WAAW;QACT,CAAA,GAAA,qJAAA,CAAA,UAAM,AAAD,EAAE;YACL,UAAU,QAAQ,GAAG,CAAC,gBAAgB;YACtC,cAAc,QAAQ,GAAG,CAAC,oBAAoB;QAChD;KACD;IACD,WAAW;QACT,MAAM,SAAQ,EAAE,OAAO,EAAE,KAAK,EAAE;YAC9B,IAAI,QAAQ,IAAI,IAAI,MAAM,GAAG,EAAE;gBAC7B,QAAQ,IAAI,CAAC,EAAE,GAAG,MAAM,GAAG;YAC7B;YACA,OAAO;QACT;QACA,MAAM,KAAI,EAAE,KAAK,EAAE,IAAI,EAAE;YACvB,IAAI,MAAM;gBACR,MAAM,GAAG,GAAG,KAAK,EAAE;YACrB;YACA,OAAO;QACT;IACF;IACA,OAAO;QACL,QAAQ;IACV;AACF", "debugId": null}}, {"offset": {"line": 131, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/NextJS/Minecraft_Web/client_nextjs/components/navigation/header.tsx"], "sourcesContent": ["import { But<PERSON> } from \"@/components/ui/button\";\nimport { auth, signOut } from \"@/lib/auth\";\nimport Link from \"next/link\";\nimport Image from \"next/image\";\n\nexport async function Header() {\n  const session = await auth();\n\n  return (\n    <header className=\"border-b border-border bg-card\">\n      <div className=\"container mx-auto px-4 py-4 flex justify-between items-center\">\n        <Link href=\"/\" className=\"flex items-center space-x-2\">\n          <div className=\"w-8 h-8 bg-primary rounded-md flex items-center justify-center\">\n            <span className=\"text-primary-foreground font-bold text-sm\">MC</span>\n          </div>\n          <h1 className=\"text-xl font-bold text-foreground\">MineCraft Blog</h1>\n        </Link>\n        \n        <nav className=\"hidden md:flex items-center space-x-6\">\n          <Link href=\"/\" className=\"text-foreground hover:text-primary transition-colors\">\n            Home\n          </Link>\n          <Link href=\"/blogs\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n            Blogs\n          </Link>\n          <Link href=\"/about\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n            About\n          </Link>\n          {session && (\n            <Link href=\"/profile\" className=\"text-muted-foreground hover:text-primary transition-colors\">\n              Profile\n            </Link>\n          )}\n        </nav>\n        \n        <div className=\"flex items-center space-x-2\">\n          {session ? (\n            <div className=\"flex items-center space-x-3\">\n              {session.user?.image && (\n                <Link href=\"/profile\">\n                  <Image\n                    src={session.user.image}\n                    alt={session.user.name || \"User avatar\"}\n                    width={32}\n                    height={32}\n                    className=\"rounded-full border border-border hover:border-primary transition-colors\"\n                  />\n                </Link>\n              )}\n              <span className=\"text-sm text-muted-foreground hidden sm:block\">\n                {session.user?.name}\n              </span>\n              <form\n                action={async () => {\n                  \"use server\";\n                  await signOut({ redirectTo: \"/\" });\n                }}\n              >\n                <Button variant=\"outline\" size=\"sm\" type=\"submit\">\n                  Sign Out\n                </Button>\n              </form>\n            </div>\n          ) : (\n            <>\n              <Button variant=\"ghost\" size=\"sm\" asChild>\n                <Link href=\"/login\">Login</Link>\n              </Button>\n              <Button size=\"sm\" asChild>\n                <Link href=\"/login\">Sign Up</Link>\n              </Button>\n            </>\n          )}\n        </div>\n      </div>\n    </header>\n  );\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;AACA;AACA;AACA;;;;;;;;MAkDwB,uCAAR;IAEE,MAAM,CAAA,GAAA,2GAAA,CAAA,UAAO,AAAD,EAAE;QAAE,YAAY;IAAI;AAClC;AAnDT,eAAe;IACpB,MAAM,UAAU,MAAM,CAAA,GAAA,2GAAA,CAAA,OAAI,AAAD;IAEzB,qBACE,8OAAC;QAAO,WAAU;kBAChB,cAAA,8OAAC;YAAI,WAAU;;8BACb,8OAAC,4JAAA,CAAA,UAAI;oBAAC,MAAK;oBAAI,WAAU;;sCACvB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAK,WAAU;0CAA4C;;;;;;;;;;;sCAE9D,8OAAC;4BAAG,WAAU;sCAAoC;;;;;;;;;;;;8BAGpD,8OAAC;oBAAI,WAAU;;sCACb,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAI,WAAU;sCAAuD;;;;;;sCAGhF,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAS,WAAU;sCAA6D;;;;;;sCAG3F,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAS,WAAU;sCAA6D;;;;;;wBAG1F,yBACC,8OAAC,4JAAA,CAAA,UAAI;4BAAC,MAAK;4BAAW,WAAU;sCAA6D;;;;;;;;;;;;8BAMjG,8OAAC;oBAAI,WAAU;8BACZ,wBACC,8OAAC;wBAAI,WAAU;;4BACZ,QAAQ,IAAI,EAAE,uBACb,8OAAC,4JAAA,CAAA,UAAI;gCAAC,MAAK;0CACT,cAAA,8OAAC,6HAAA,CAAA,UAAK;oCACJ,KAAK,QAAQ,IAAI,CAAC,KAAK;oCACvB,KAAK,QAAQ,IAAI,CAAC,IAAI,IAAI;oCAC1B,OAAO;oCACP,QAAQ;oCACR,WAAU;;;;;;;;;;;0CAIhB,8OAAC;gCAAK,WAAU;0CACb,QAAQ,IAAI,EAAE;;;;;;0CAEjB,8OAAC;gCACC,QAAQ,+OAAA;0CAKR,cAAA,8OAAC,2HAAA,CAAA,SAAM;oCAAC,SAAQ;oCAAU,MAAK;oCAAK,MAAK;8CAAS;;;;;;;;;;;;;;;;6CAMtD;;0CACE,8OAAC,2HAAA,CAAA,SAAM;gCAAC,SAAQ;gCAAQ,MAAK;gCAAK,OAAO;0CACvC,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAS;;;;;;;;;;;0CAEtB,8OAAC,2HAAA,CAAA,SAAM;gCAAC,MAAK;gCAAK,OAAO;0CACvB,cAAA,8OAAC,4JAAA,CAAA,UAAI;oCAAC,MAAK;8CAAS;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQpC", "debugId": null}}, {"offset": {"line": 352, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/NextJS/Minecraft_Web/client_nextjs/.next-internal/server/app/page/actions.js%20%28server%20actions%20loader%29"], "sourcesContent": ["export {$$RSC_SERVER_ACTION_0 as '00344232de16b139eb0d4d4321b3e0bd2d562e7edc'} from 'ACTIONS_MODULE0'\n"], "names": [], "mappings": ";AAAA", "debugId": null}}, {"offset": {"line": 404, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/NextJS/Minecraft_Web/client_nextjs/components/ui/card.tsx"], "sourcesContent": ["import * as React from \"react\"\n\nimport { cn } from \"@/lib/utils\"\n\nfunction Card({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card\"\n      className={cn(\n        \"bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardHeader({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-header\"\n      className={cn(\n        \"@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardTitle({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-title\"\n      className={cn(\"leading-none font-semibold\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardDescription({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-description\"\n      className={cn(\"text-muted-foreground text-sm\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardAction({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-action\"\n      className={cn(\n        \"col-start-2 row-span-2 row-start-1 self-start justify-self-end\",\n        className\n      )}\n      {...props}\n    />\n  )\n}\n\nfunction CardContent({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-content\"\n      className={cn(\"px-6\", className)}\n      {...props}\n    />\n  )\n}\n\nfunction CardFooter({ className, ...props }: React.ComponentProps<\"div\">) {\n  return (\n    <div\n      data-slot=\"card-footer\"\n      className={cn(\"flex items-center px-6 [.border-t]:pt-6\", className)}\n      {...props}\n    />\n  )\n}\n\nexport {\n  Card,\n  CardHeader,\n  CardFooter,\n  CardTitle,\n  CardAction,\n  CardDescription,\n  CardContent,\n}\n"], "names": [], "mappings": ";;;;;;;;;;AAEA;;;AAEA,SAAS,KAAK,EAAE,SAAS,EAAE,GAAG,OAAoC;IAChE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,qFACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,8JACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,UAAU,EAAE,SAAS,EAAE,GAAG,OAAoC;IACrE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,8BAA8B;QAC3C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,gBAAgB,EAAE,SAAS,EAAE,GAAG,OAAoC;IAC3E,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,iCAAiC;QAC9C,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EACV,kEACA;QAED,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,YAAY,EAAE,SAAS,EAAE,GAAG,OAAoC;IACvE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,QAAQ;QACrB,GAAG,KAAK;;;;;;AAGf;AAEA,SAAS,WAAW,EAAE,SAAS,EAAE,GAAG,OAAoC;IACtE,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,2CAA2C;QACxD,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 501, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/NextJS/Minecraft_Web/client_nextjs/components/ui/badge.tsx"], "sourcesContent": ["import * as React from \"react\"\nimport { Slot } from \"@radix-ui/react-slot\"\nimport { cva, type VariantProps } from \"class-variance-authority\"\n\nimport { cn } from \"@/lib/utils\"\n\nconst badgeVariants = cva(\n  \"inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden\",\n  {\n    variants: {\n      variant: {\n        default:\n          \"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90\",\n        secondary:\n          \"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90\",\n        destructive:\n          \"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60\",\n        outline:\n          \"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground\",\n      },\n    },\n    defaultVariants: {\n      variant: \"default\",\n    },\n  }\n)\n\nfunction Badge({\n  className,\n  variant,\n  asChild = false,\n  ...props\n}: React.ComponentProps<\"span\"> &\n  VariantProps<typeof badgeVariants> & { asChild?: boolean }) {\n  const Comp = asChild ? Slot : \"span\"\n\n  return (\n    <Comp\n      data-slot=\"badge\"\n      className={cn(badgeVariants({ variant }), className)}\n      {...props}\n    />\n  )\n}\n\nexport { Badge, badgeVariants }\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAEA;;;;;AAEA,MAAM,gBAAgB,CAAA,GAAA,gKAAA,CAAA,MAAG,AAAD,EACtB,kZACA;IACE,UAAU;QACR,SAAS;YACP,SACE;YACF,WACE;YACF,aACE;YACF,SACE;QACJ;IACF;IACA,iBAAiB;QACf,SAAS;IACX;AACF;AAGF,SAAS,MAAM,EACb,SAAS,EACT,OAAO,EACP,UAAU,KAAK,EACf,GAAG,OAEuD;IAC1D,MAAM,OAAO,UAAU,gKAAA,CAAA,OAAI,GAAG;IAE9B,qBACE,8OAAC;QACC,aAAU;QACV,WAAW,CAAA,GAAA,4GAAA,CAAA,KAAE,AAAD,EAAE,cAAc;YAAE;QAAQ,IAAI;QACzC,GAAG,KAAK;;;;;;AAGf", "debugId": null}}, {"offset": {"line": 547, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/NextJS/Minecraft_Web/client_nextjs/components/ui/separator.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Separator = registerClientReference(\n    function() { throw new Error(\"Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/separator.tsx <module evaluation>\",\n    \"Separator\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,6DACA", "debugId": null}}, {"offset": {"line": 561, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/NextJS/Minecraft_Web/client_nextjs/components/ui/separator.tsx/proxy.mjs"], "sourcesContent": ["import { registerClientReference } from \"react-server-dom-turbopack/server.edge\";\nexport const Separator = registerClientReference(\n    function() { throw new Error(\"Attempted to call Separator() from the server but Separator is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.\"); },\n    \"[project]/components/ui/separator.tsx\",\n    \"Separator\",\n);\n"], "names": [], "mappings": ";;;AAAA;;AACO,MAAM,YAAY,CAAA,GAAA,qPAAA,CAAA,0BAAuB,AAAD,EAC3C;IAAa,MAAM,IAAI,MAAM;AAAkO,GAC/P,yCACA", "debugId": null}}, {"offset": {"line": 575, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 585, "column": 0}, "map": {"version": 3, "sources": ["file:///D:/NextJS/Minecraft_Web/client_nextjs/app/page.tsx"], "sourcesContent": ["import { <PERSON><PERSON> } from \"@/components/ui/button\";\nimport { Card, CardContent, CardDescription, CardHeader, CardTitle } from \"@/components/ui/card\";\nimport { Badge } from \"@/components/ui/badge\";\nimport { Separator } from \"@/components/ui/separator\";\nimport { Head<PERSON> } from \"@/components/navigation/header\";\nimport Link from \"next/link\";\n\nexport default function Home() {\n  return (\n    <div className=\"min-h-screen bg-background\">\n      <Header />\n\n      <main>\n        {/* Hero Section */}\n        <section className=\"py-20 px-4 text-center bg-gradient-to-br from-primary/5 via-background to-secondary/10\">\n          <div className=\"container mx-auto max-w-4xl\">\n            <Badge variant=\"secondary\" className=\"mb-4\">\n              Welcome to the Community\n            </Badge>\n            <h1 className=\"text-4xl md:text-6xl font-bold text-foreground mb-6\">\n              Share Your <span className=\"text-primary\">Minecraft</span> Adventures\n            </h1>\n            <p className=\"text-xl text-muted-foreground mb-8 max-w-2xl mx-auto\">\n              Join thousands of Minecraft enthusiasts sharing builds, tutorials, mods, and epic adventures.\n              Create your own blog and become part of our amazing community.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Button size=\"lg\" className=\"text-lg px-8\">\n                Start Blogging\n              </Button>\n              <Button variant=\"outline\" size=\"lg\" className=\"text-lg px-8\">\n                Explore Blogs\n              </Button>\n            </div>\n          </div>\n        </section>\n\n        {/* Welcome Section */}\n        <section className=\"py-16 px-4 bg-background\">\n          <div className=\"container mx-auto max-w-6xl\">\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-foreground mb-4\">\n                Welcome to MineCraft Blog\n              </h2>\n              <p className=\"text-lg text-muted-foreground max-w-2xl mx-auto\">\n                Your ultimate destination for everything Minecraft. Whether you're a builder,\n                redstone engineer, or adventure seeker, this is your space to share and discover.\n              </p>\n            </div>\n\n            <div className=\"grid md:grid-cols-3 gap-8\">\n              <Card className=\"text-center border-2 hover:border-primary/20 transition-colors\">\n                <CardHeader>\n                  <div className=\"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                    <span className=\"text-2xl\">🏗️</span>\n                  </div>\n                  <CardTitle>Share Your Builds</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <CardDescription>\n                    Showcase your amazing creations, from simple houses to massive castles.\n                    Get feedback and inspire others with your architectural masterpieces.\n                  </CardDescription>\n                </CardContent>\n              </Card>\n\n              <Card className=\"text-center border-2 hover:border-primary/20 transition-colors\">\n                <CardHeader>\n                  <div className=\"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                    <span className=\"text-2xl\">📚</span>\n                  </div>\n                  <CardTitle>Write Tutorials</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <CardDescription>\n                    Help fellow players learn new techniques, redstone contraptions,\n                    or survival strategies. Share your knowledge and become a community teacher.\n                  </CardDescription>\n                </CardContent>\n              </Card>\n\n              <Card className=\"text-center border-2 hover:border-primary/20 transition-colors\">\n                <CardHeader>\n                  <div className=\"w-12 h-12 bg-primary/10 rounded-lg flex items-center justify-center mx-auto mb-4\">\n                    <span className=\"text-2xl\">🎮</span>\n                  </div>\n                  <CardTitle>Document Adventures</CardTitle>\n                </CardHeader>\n                <CardContent>\n                  <CardDescription>\n                    Chronicle your epic journeys, multiplayer experiences, and memorable moments.\n                    Turn your gameplay into engaging stories for the community.\n                  </CardDescription>\n                </CardContent>\n              </Card>\n            </div>\n          </div>\n        </section>\n\n        {/* About Us Section */}\n        <section className=\"py-16 px-4 bg-muted/20\">\n          <div className=\"container mx-auto max-w-4xl text-center\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-foreground mb-6\">\n              About Our Community\n            </h2>\n            <p className=\"text-lg text-muted-foreground mb-8\">\n              MineCraft Blog was created by passionate Minecraft players for passionate Minecraft players.\n              We believe that every player has unique experiences and knowledge worth sharing.\n            </p>\n            <div className=\"grid md:grid-cols-2 gap-8 text-left\">\n              <Card className=\"p-6 bg-card/50 backdrop-blur-sm\">\n                <h3 className=\"text-xl font-semibold text-foreground mb-3\">Our Mission</h3>\n                <p className=\"text-muted-foreground\">\n                  To create a welcoming space where Minecraft enthusiasts can share their creativity,\n                  learn from each other, and build lasting friendships through their shared love of the game.\n                </p>\n              </Card>\n              <Card className=\"p-6 bg-card/50 backdrop-blur-sm\">\n                <h3 className=\"text-xl font-semibold text-foreground mb-3\">Our Values</h3>\n                <p className=\"text-muted-foreground\">\n                  We promote creativity, respect, and collaboration. Every voice matters in our community,\n                  from beginners sharing their first builds to veterans teaching advanced techniques.\n                </p>\n              </Card>\n            </div>\n          </div>\n        </section>\n\n        {/* Features Section */}\n        <section className=\"py-16 px-4 bg-gradient-to-r from-secondary/5 to-accent/5\">\n          <div className=\"container mx-auto max-w-6xl\">\n            <div className=\"text-center mb-12\">\n              <h2 className=\"text-3xl md:text-4xl font-bold text-foreground mb-4\">\n                Why Choose MineCraft Blog?\n              </h2>\n              <p className=\"text-lg text-muted-foreground\">\n                Everything you need to share your Minecraft journey with the world\n              </p>\n            </div>\n\n            <div className=\"grid md:grid-cols-2 lg:grid-cols-4 gap-6\">\n              <Card className=\"text-center p-6 bg-card/80 backdrop-blur-sm hover:bg-card transition-colors\">\n                <div className=\"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <span className=\"text-3xl\">✍️</span>\n                </div>\n                <h3 className=\"font-semibold text-foreground mb-2\">Easy Writing</h3>\n                <p className=\"text-sm text-muted-foreground\">\n                  Simple, intuitive editor for creating beautiful blog posts\n                </p>\n              </Card>\n\n              <Card className=\"text-center p-6 bg-card/80 backdrop-blur-sm hover:bg-card transition-colors\">\n                <div className=\"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <span className=\"text-3xl\">📸</span>\n                </div>\n                <h3 className=\"font-semibold text-foreground mb-2\">Image Support</h3>\n                <p className=\"text-sm text-muted-foreground\">\n                  Upload and showcase your screenshots and builds\n                </p>\n              </Card>\n\n              <Card className=\"text-center p-6 bg-card/80 backdrop-blur-sm hover:bg-card transition-colors\">\n                <div className=\"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <span className=\"text-3xl\">👥</span>\n                </div>\n                <h3 className=\"font-semibold text-foreground mb-2\">Community</h3>\n                <p className=\"text-sm text-muted-foreground\">\n                  Connect with like-minded Minecraft enthusiasts\n                </p>\n              </Card>\n\n              <Card className=\"text-center p-6 bg-card/80 backdrop-blur-sm hover:bg-card transition-colors\">\n                <div className=\"w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4\">\n                  <span className=\"text-3xl\">🔒</span>\n                </div>\n                <h3 className=\"font-semibold text-foreground mb-2\">Secure</h3>\n                <p className=\"text-sm text-muted-foreground\">\n                  Safe and secure platform for all your content\n                </p>\n              </Card>\n            </div>\n          </div>\n        </section>\n\n        {/* Call to Action Section */}\n        <section className=\"py-20 px-4 bg-gradient-to-t from-primary/10 via-primary/5 to-background\">\n          <div className=\"container mx-auto max-w-4xl text-center\">\n            <h2 className=\"text-3xl md:text-4xl font-bold text-foreground mb-6\">\n              Ready to Share Your Story?\n            </h2>\n            <p className=\"text-lg text-muted-foreground mb-8\">\n              Join our growing community of Minecraft bloggers. Create your account today and start sharing\n              your adventures, builds, and knowledge with players around the world.\n            </p>\n            <div className=\"flex flex-col sm:flex-row gap-4 justify-center\">\n              <Button size=\"lg\" className=\"text-lg px-8\">\n                Create Account\n              </Button>\n              <Button variant=\"outline\" size=\"lg\" className=\"text-lg px-8\">\n                Learn More\n              </Button>\n            </div>\n            <p className=\"text-sm text-muted-foreground mt-4\">\n              Already have an account? <Link href=\"/login\" className=\"text-primary hover:underline\">Sign in here</Link>\n            </p>\n          </div>\n        </section>\n      </main>\n\n      {/* Footer */}\n      <footer className=\"border-t border-border bg-card py-12 px-4\">\n        <div className=\"container mx-auto max-w-6xl\">\n          <div className=\"grid md:grid-cols-4 gap-8\">\n            <div>\n              <div className=\"flex items-center space-x-2 mb-4\">\n                <div className=\"w-8 h-8 bg-primary rounded-md flex items-center justify-center\">\n                  <span className=\"text-primary-foreground font-bold text-sm\">MC</span>\n                </div>\n                <h3 className=\"font-bold text-foreground\">MineCraft Blog</h3>\n              </div>\n              <p className=\"text-sm text-muted-foreground\">\n                The ultimate platform for Minecraft enthusiasts to share their adventures and connect with the community.\n              </p>\n            </div>\n\n            <div>\n              <h4 className=\"font-semibold text-foreground mb-3\">Community</h4>\n              <ul className=\"space-y-2 text-sm text-muted-foreground\">\n                <li><Link href=\"/blogs\" className=\"hover:text-primary transition-colors\">Browse Blogs</Link></li>\n                <li><Link href=\"/authors\" className=\"hover:text-primary transition-colors\">Top Authors</Link></li>\n                <li><Link href=\"/categories\" className=\"hover:text-primary transition-colors\">Categories</Link></li>\n              </ul>\n            </div>\n\n            <div>\n              <h4 className=\"font-semibold text-foreground mb-3\">Support</h4>\n              <ul className=\"space-y-2 text-sm text-muted-foreground\">\n                <li><Link href=\"/help\" className=\"hover:text-primary transition-colors\">Help Center</Link></li>\n                <li><Link href=\"/guidelines\" className=\"hover:text-primary transition-colors\">Community Guidelines</Link></li>\n                <li><Link href=\"/contact\" className=\"hover:text-primary transition-colors\">Contact Us</Link></li>\n              </ul>\n            </div>\n\n            <div>\n              <h4 className=\"font-semibold text-foreground mb-3\">Legal</h4>\n              <ul className=\"space-y-2 text-sm text-muted-foreground\">\n                <li><Link href=\"/privacy\" className=\"hover:text-primary transition-colors\">Privacy Policy</Link></li>\n                <li><Link href=\"/terms\" className=\"hover:text-primary transition-colors\">Terms of Service</Link></li>\n                <li><Link href=\"/cookies\" className=\"hover:text-primary transition-colors\">Cookie Policy</Link></li>\n              </ul>\n            </div>\n          </div>\n\n          <Separator className=\"my-8\" />\n\n          <div className=\"flex flex-col md:flex-row justify-between items-center\">\n            <p className=\"text-sm text-muted-foreground\">\n              © 2024 MineCraft Blog. All rights reserved.\n            </p>\n            <p className=\"text-sm text-muted-foreground\">\n              Made with ❤️ for the Minecraft community\n            </p>\n          </div>\n        </div>\n      </footer>\n    </div>\n  );\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;AACA;AACA;AACA;AACA;;;;;;;;AAEe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC,mIAAA,CAAA,SAAM;;;;;0BAEP,8OAAC;;kCAEC,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC,0HAAA,CAAA,QAAK;oCAAC,SAAQ;oCAAY,WAAU;8CAAO;;;;;;8CAG5C,8OAAC;oCAAG,WAAU;;wCAAsD;sDACvD,8OAAC;4CAAK,WAAU;sDAAe;;;;;;wCAAgB;;;;;;;8CAE5D,8OAAC;oCAAE,WAAU;8CAAuD;;;;;;8CAIpE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,2HAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAAe;;;;;;sDAG3C,8OAAC,2HAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;sDAAe;;;;;;;;;;;;;;;;;;;;;;;kCAQnE,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsD;;;;;;sDAGpE,8OAAC;4CAAE,WAAU;sDAAkD;;;;;;;;;;;;8CAMjE,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,yHAAA,CAAA,OAAI;4CAAC,WAAU;;8DACd,8OAAC,yHAAA,CAAA,aAAU;;sEACT,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAAW;;;;;;;;;;;sEAE7B,8OAAC,yHAAA,CAAA,YAAS;sEAAC;;;;;;;;;;;;8DAEb,8OAAC,yHAAA,CAAA,cAAW;8DACV,cAAA,8OAAC,yHAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;;;;;;sDAOrB,8OAAC,yHAAA,CAAA,OAAI;4CAAC,WAAU;;8DACd,8OAAC,yHAAA,CAAA,aAAU;;sEACT,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAAW;;;;;;;;;;;sEAE7B,8OAAC,yHAAA,CAAA,YAAS;sEAAC;;;;;;;;;;;;8DAEb,8OAAC,yHAAA,CAAA,cAAW;8DACV,cAAA,8OAAC,yHAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;;;;;;sDAOrB,8OAAC,yHAAA,CAAA,OAAI;4CAAC,WAAU;;8DACd,8OAAC,yHAAA,CAAA,aAAU;;sEACT,8OAAC;4DAAI,WAAU;sEACb,cAAA,8OAAC;gEAAK,WAAU;0EAAW;;;;;;;;;;;sEAE7B,8OAAC,yHAAA,CAAA,YAAS;sEAAC;;;;;;;;;;;;8DAEb,8OAAC,yHAAA,CAAA,cAAW;8DACV,cAAA,8OAAC,yHAAA,CAAA,kBAAe;kEAAC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAW3B,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;8CAGpE,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAIlD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,yHAAA,CAAA,OAAI;4CAAC,WAAU;;8DACd,8OAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;sDAKvC,8OAAC,yHAAA,CAAA,OAAI;4CAAC,WAAU;;8DACd,8OAAC;oDAAG,WAAU;8DAA6C;;;;;;8DAC3D,8OAAC;oDAAE,WAAU;8DAAwB;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCAU7C,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDAAsD;;;;;;sDAGpE,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAK/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,yHAAA,CAAA,OAAI;4CAAC,WAAU;;8DACd,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAW;;;;;;;;;;;8DAE7B,8OAAC;oDAAG,WAAU;8DAAqC;;;;;;8DACnD,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;sDAK/C,8OAAC,yHAAA,CAAA,OAAI;4CAAC,WAAU;;8DACd,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAW;;;;;;;;;;;8DAE7B,8OAAC;oDAAG,WAAU;8DAAqC;;;;;;8DACnD,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;sDAK/C,8OAAC,yHAAA,CAAA,OAAI;4CAAC,WAAU;;8DACd,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAW;;;;;;;;;;;8DAE7B,8OAAC;oDAAG,WAAU;8DAAqC;;;;;;8DACnD,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;sDAK/C,8OAAC,yHAAA,CAAA,OAAI;4CAAC,WAAU;;8DACd,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAAW;;;;;;;;;;;8DAE7B,8OAAC;oDAAG,WAAU;8DAAqC;;;;;;8DACnD,8OAAC;oDAAE,WAAU;8DAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;kCASrD,8OAAC;wBAAQ,WAAU;kCACjB,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAG,WAAU;8CAAsD;;;;;;8CAGpE,8OAAC;oCAAE,WAAU;8CAAqC;;;;;;8CAIlD,8OAAC;oCAAI,WAAU;;sDACb,8OAAC,2HAAA,CAAA,SAAM;4CAAC,MAAK;4CAAK,WAAU;sDAAe;;;;;;sDAG3C,8OAAC,2HAAA,CAAA,SAAM;4CAAC,SAAQ;4CAAU,MAAK;4CAAK,WAAU;sDAAe;;;;;;;;;;;;8CAI/D,8OAAC;oCAAE,WAAU;;wCAAqC;sDACvB,8OAAC,4JAAA,CAAA,UAAI;4CAAC,MAAK;4CAAS,WAAU;sDAA+B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;0BAO9F,8OAAC;gBAAO,WAAU;0BAChB,cAAA,8OAAC;oBAAI,WAAU;;sCACb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;;sDACC,8OAAC;4CAAI,WAAU;;8DACb,8OAAC;oDAAI,WAAU;8DACb,cAAA,8OAAC;wDAAK,WAAU;kEAA4C;;;;;;;;;;;8DAE9D,8OAAC;oDAAG,WAAU;8DAA4B;;;;;;;;;;;;sDAE5C,8OAAC;4CAAE,WAAU;sDAAgC;;;;;;;;;;;;8CAK/C,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAuC;;;;;;;;;;;8DACzE,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAuC;;;;;;;;;;;8DAC3E,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAc,WAAU;kEAAuC;;;;;;;;;;;;;;;;;;;;;;;8CAIlF,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAQ,WAAU;kEAAuC;;;;;;;;;;;8DACxE,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAc,WAAU;kEAAuC;;;;;;;;;;;8DAC9E,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAuC;;;;;;;;;;;;;;;;;;;;;;;8CAI/E,8OAAC;;sDACC,8OAAC;4CAAG,WAAU;sDAAqC;;;;;;sDACnD,8OAAC;4CAAG,WAAU;;8DACZ,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAuC;;;;;;;;;;;8DAC3E,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAS,WAAU;kEAAuC;;;;;;;;;;;8DACzE,8OAAC;8DAAG,cAAA,8OAAC,4JAAA,CAAA,UAAI;wDAAC,MAAK;wDAAW,WAAU;kEAAuC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;sCAKjF,8OAAC,8HAAA,CAAA,YAAS;4BAAC,WAAU;;;;;;sCAErB,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;8CAG7C,8OAAC;oCAAE,WAAU;8CAAgC;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAQzD", "debugId": null}}]}