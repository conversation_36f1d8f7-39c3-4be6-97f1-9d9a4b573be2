import * as dynamoose from 'dynamoose'
import { Item } from 'dynamoose/dist/Item'
import envConfig from '@/config'

const sessionSchema = new dynamoose.Schema(
  {
    id: {
      type: String,
      hashKey: true
    },
    token: {
      type: String,
      index: [
        {
          type: 'global',
          name: 'tokenIndex'
        },
        // {
        //   type: 'global',
        //   name: 'token_recent_idx',
        //   rangeKey: 'createdAt'
        // }
      ],
      required: true
    },
    accountId: {
      type: String,
      required: true,
      index: {
        type: 'global',
        name: 'accountIdIndex'
      }
    },
    expiresAt: {
      type: Date,
      required: true
      // Add a get transformer to ensure it's always a Date object
      // get: (value: unknown) => (value instanceof Date ? value : new Date(value))
    },
    createdAt: {
      type: Date,
      default: () => new Date()
    }
  },
  {
    timestamps: false
  }
)

export interface ISession extends Item {
  id: string
  token: string
  accountId: string
  expiresAt: Date
  createdAt: Date
}

export const Session = dynamoose.model<ISession>(envConfig.SESSIONS_TABLE, sessionSchema)

// Utility functions
export class SessionUtils {
  static async findValidByToken(req_token: string): Promise<ISession | null> {
    const sessions = await Session.scan({
      token: req_token,
      expiresAt: { gt: Date.now() }
    }).exec()
    return sessions.count ? sessions[0] : null
  }

  static async deleteByToken(req_token: string): Promise<void> {
    const sessions = await Session.scan({ token: req_token }).exec()
    if (sessions.count) {
      await Session.delete(sessions[0].id)
    }
  }

  static async cleanupExpired(): Promise<void> {
    const expiredSessions = await Session.scan({
      expiresAt: { lt: Date.now() }
    }).exec()

    for (const session of expiredSessions) {
      await Session.delete(session.id)
    }
  }
}
