import { FastifyRequest, FastifyReply } from 'fastify';
import { Session } from '@/models/session.model';
import { Account } from '@/models/account.model';
import {Role} from '@/models/account.model'

export async function authenticate(request: FastifyRequest, reply: FastifyReply) {
  const token = request.cookies.sessionToken || request.headers.authorization?.replace('Bearer ', '');

  if (!token) {
    reply.code(401).send({ error: 'Authentication required' });
    return;
  }

  const sessions = await Session.scan({
    token: token,
    expiresAt: { gt: new Date() }
  }).exec();

  if (!sessions.count) {
    reply.code(401).send({ error: 'Invalid or expired session' });
    return;
  }

  const session = sessions[0];
  const accounts = await Account.scan({
    id: session.accountId
  }).exec();

  if (!accounts.count) {
    reply.code(401).send({ error: 'Account not found' });
    return;
  }

  request.account = {
    ...accounts[0],
    role: accounts[0].role as Role
  };
  // request.sessionId = session.id;
}